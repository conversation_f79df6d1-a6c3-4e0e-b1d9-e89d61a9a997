{"name": "semantic-search-renderer", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "axios": "^1.6.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "tailwind-merge": "^2.0.0", "react-router-dom": "^6.8.0", "framer-motion": "^10.16.0", "react-hotkeys-hook": "^4.4.1", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}, "homepage": "./"}