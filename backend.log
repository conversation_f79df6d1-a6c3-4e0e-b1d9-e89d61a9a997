2025-07-18 23:09:12,704 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 23:09:26,314 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 23:09:26,334 - main - INFO - Initializing document search backend...
2025-07-18 23:09:26,335 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 23:09:26,337 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 23:09:26,337 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 23:09:32,862 - database - INFO - Connected to existing table: documents
2025-07-18 23:09:32,877 - database - INFO - Vector store initialized successfully
2025-07-18 23:09:32,889 - document_processor - INFO - Document processor initialized
2025-07-18 23:09:34,450 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 23:09:34,453 - folder_manager - INFO - Starting folder monitoring...
2025-07-18 23:09:34,454 - folder_manager - WARNING - Folder does not exist, skipping monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-18 23:09:34,457 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 23:09:34,458 - folder_manager - INFO - Monitoring 2 folders
2025-07-18 23:09:34,465 - api_service - INFO - API server started successfully
2025-07-18 23:09:34,494 - folder_manager - INFO - Starting background document processor...
2025-07-18 23:09:51,985 - main - INFO - Search for 'w' returned 7 results
2025-07-18 23:09:52,595 - main - INFO - Search for 'wa' returned 7 results
2025-07-18 23:09:53,016 - main - INFO - Search for 'was' returned 7 results
2025-07-18 23:09:53,150 - main - INFO - Search for 'wasa' returned 7 results
2025-07-18 23:09:53,626 - main - INFO - Search for 'was' returned 7 results
2025-07-18 23:09:53,770 - main - INFO - Search for 'wa' returned 7 results
2025-07-18 23:09:53,903 - main - INFO - Search for 'w' returned 7 results
2025-07-18 23:10:01,004 - main - INFO - Search for 'N' returned 7 results
2025-07-18 23:10:03,210 - folder_manager - INFO - File created detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt
2025-07-18 23:10:03,220 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt (trigger: created)
2025-07-18 23:10:03,221 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt
2025-07-18 23:10:03,222 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt (trigger: modified)
2025-07-18 23:10:04,059 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt
2025-07-18 23:10:04,061 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt
2025-07-18 23:10:04,063 - document_processor - INFO - Processed sample_document.txt: 2 chunks created
2025-07-18 23:10:04,064 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt: 2 chunks, type: <class 'list'>
2025-07-18 23:10:04,064 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:10:04,065 - database - INFO - add_document called with 2 chunks, type: <class 'list'>
2025-07-18 23:10:04,065 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:10:04,152 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\sample_document.txt', 'filename': 'sample_document.txt', 'extension': '.txt', 'file_size': 564, 'modified_time': 1752862203.2095788, 'format': 'text', 'lines': 15, 'words': 85, 'chunk_index': 0, 'total_chunks': 2, 'chunk_size': 441}
2025-07-18 23:10:04,152 - database - INFO - Processing chunk 1: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\sample_document.txt', 'filename': 'sample_document.txt', 'extension': '.txt', 'file_size': 564, 'modified_time': 1752862203.2095788, 'format': 'text', 'lines': 15, 'words': 85, 'chunk_index': 1, 'total_chunks': 2, 'chunk_size': 216}
2025-07-18 23:10:04,175 - database - INFO - Added 2 chunks for document: sample_document.txt
2025-07-18 23:10:04,176 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt (0 chunks)
2025-07-18 23:10:04,176 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt
2025-07-18 23:10:04,177 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt
2025-07-18 23:10:04,179 - document_processor - INFO - Processed sample_document.txt: 2 chunks created
2025-07-18 23:10:04,179 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt: 2 chunks, type: <class 'list'>
2025-07-18 23:10:04,179 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:10:04,179 - database - INFO - add_document called with 2 chunks, type: <class 'list'>
2025-07-18 23:10:04,179 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:10:04,243 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\sample_document.txt', 'filename': 'sample_document.txt', 'extension': '.txt', 'file_size': 564, 'modified_time': 1752862203.2095788, 'format': 'text', 'lines': 15, 'words': 85, 'chunk_index': 0, 'total_chunks': 2, 'chunk_size': 441}
2025-07-18 23:10:04,243 - database - INFO - Processing chunk 1: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\sample_document.txt', 'filename': 'sample_document.txt', 'extension': '.txt', 'file_size': 564, 'modified_time': 1752862203.2095788, 'format': 'text', 'lines': 15, 'words': 85, 'chunk_index': 1, 'total_chunks': 2, 'chunk_size': 216}
2025-07-18 23:10:04,262 - database - INFO - Added 2 chunks for document: sample_document.txt
2025-07-18 23:10:04,262 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt (0 chunks)
2025-07-18 23:10:05,499 - folder_manager - INFO - Removed file from store: E:\PROJECT\Semantic_Search_Assistant\test_docs\New folder
2025-07-18 23:10:25,214 - folder_manager - INFO - Removed file from store: E:\PROJECT\Semantic_Search_Assistant\test_docs\sample_document.txt
2025-07-18 23:10:31,675 - folder_manager - INFO - File created detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:10:31,676 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt (trigger: created)
2025-07-18 23:10:32,687 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:10:32,688 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:10:32,690 - document_processor - INFO - Processed New Text Document.txt: 0 chunks created
2025-07-18 23:10:32,690 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt: 0 chunks, type: <class 'list'>
2025-07-18 23:10:32,690 - database - INFO - add_document called with 0 chunks, type: <class 'list'>
2025-07-18 23:10:32,691 - database - INFO - No chunks to add for E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:10:32,691 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt (0 chunks)
2025-07-18 23:10:32,837 - main - INFO - Search for 'Nh' returned 7 results
2025-07-18 23:10:32,906 - main - INFO - Search for 'Nhe' returned 7 results
2025-07-18 23:10:33,103 - main - INFO - Search for 'Nhel' returned 7 results
2025-07-18 23:10:33,216 - main - INFO - Search for 'Nhell' returned 7 results
2025-07-18 23:10:33,366 - main - INFO - Search for 'Nhello' returned 7 results
2025-07-18 23:10:34,359 - main - INFO - Search for 'Nhelloi' returned 7 results
2025-07-18 23:10:35,346 - main - INFO - Search for 'Nhello' returned 7 results
2025-07-18 23:10:35,464 - main - INFO - Search for 'Nhell' returned 7 results
2025-07-18 23:10:35,683 - main - INFO - Search for 'Nhel' returned 7 results
2025-07-18 23:10:36,494 - main - INFO - Search for 'Nhelo' returned 7 results
2025-07-18 23:10:36,599 - main - INFO - Search for 'Nheloo' returned 7 results
2025-07-18 23:10:36,710 - main - INFO - Search for 'Nhelooo' returned 7 results
2025-07-18 23:10:36,820 - main - INFO - Search for 'Nheloooo' returned 7 results
2025-07-18 23:10:41,124 - main - INFO - Search for 'Nheloooob' returned 7 results
2025-07-18 23:10:41,228 - main - INFO - Search for 'Nheloooobi' returned 7 results
2025-07-18 23:10:41,413 - main - INFO - Search for 'Nheloooobil' returned 7 results
2025-07-18 23:10:41,510 - main - INFO - Search for 'Nheloooobila' returned 7 results
2025-07-18 23:10:41,558 - main - INFO - Search for 'Nheloooobilal' returned 7 results
2025-07-18 23:10:41,986 - main - INFO - Search for 'k' returned 7 results
2025-07-18 23:10:42,326 - main - INFO - Search for 'kh' returned 7 results
2025-07-18 23:10:42,373 - main - INFO - Search for 'kha' returned 7 results
2025-07-18 23:10:42,463 - main - INFO - Search for 'khan' returned 7 results
2025-07-18 23:10:43,164 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:43,165 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (trigger: modified)
2025-07-18 23:10:43,196 - main - INFO - Search for 'khans' returned 7 results
2025-07-18 23:10:43,760 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:43,851 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:43,872 - document_processor - INFO - Processed hellooooo.txt: 1 chunks created
2025-07-18 23:10:43,883 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt: 1 chunks, type: <class 'list'>
2025-07-18 23:10:43,887 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:10:43,897 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 23:10:43,899 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:10:43,940 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\hellooooo.txt', 'filename': 'hellooooo.txt', 'extension': '.txt', 'file_size': 10, 'modified_time': 1752862243.162792, 'format': 'text', 'lines': 1, 'words': 2, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 10}
2025-07-18 23:10:43,951 - database - INFO - Added 1 chunks for document: hellooooo.txt
2025-07-18 23:10:43,951 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (0 chunks)
2025-07-18 23:10:43,971 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:43,972 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (trigger: modified)
2025-07-18 23:10:44,009 - main - INFO - Search for 'khanss' returned 8 results
2025-07-18 23:10:45,178 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:45,180 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:45,183 - document_processor - INFO - Processed hellooooo.txt: 1 chunks created
2025-07-18 23:10:45,183 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt: 1 chunks, type: <class 'list'>
2025-07-18 23:10:45,184 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:10:45,184 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 23:10:45,185 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:10:45,206 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\hellooooo.txt', 'filename': 'hellooooo.txt', 'extension': '.txt', 'file_size': 10, 'modified_time': 1752862243.9697566, 'format': 'text', 'lines': 1, 'words': 2, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 10}
2025-07-18 23:10:45,224 - database - INFO - Added 1 chunks for document: hellooooo.txt
2025-07-18 23:10:45,225 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (0 chunks)
2025-07-18 23:10:48,449 - main - INFO - Search for 'a' returned 7 results
2025-07-18 23:10:48,570 - main - INFO - Search for 'an' returned 7 results
2025-07-18 23:10:48,646 - main - INFO - Search for 'ans' returned 7 results
2025-07-18 23:10:50,019 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:50,020 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (trigger: modified)
2025-07-18 23:10:50,048 - main - INFO - Search for 's' returned 7 results
2025-07-18 23:10:50,150 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:50,151 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:50,151 - document_processor - INFO - Processed hellooooo.txt: 1 chunks created
2025-07-18 23:10:50,152 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt: 1 chunks, type: <class 'list'>
2025-07-18 23:10:50,152 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:10:50,152 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 23:10:50,152 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:10:50,163 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\hellooooo.txt', 'filename': 'hellooooo.txt', 'extension': '.txt', 'file_size': 17, 'modified_time': 1752862250.0185502, 'format': 'text', 'lines': 1, 'words': 3, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 14}
2025-07-18 23:10:50,170 - database - INFO - Added 1 chunks for document: hellooooo.txt
2025-07-18 23:10:50,171 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (0 chunks)
2025-07-18 23:10:58,453 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:58,454 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (trigger: modified)
2025-07-18 23:10:58,510 - main - INFO - Search for 'ss' returned 8 results
2025-07-18 23:10:58,778 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:58,780 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:58,782 - document_processor - INFO - Processed hellooooo.txt: 1 chunks created
2025-07-18 23:10:58,782 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt: 1 chunks, type: <class 'list'>
2025-07-18 23:10:58,783 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:10:58,783 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 23:10:58,784 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:10:58,807 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\hellooooo.txt', 'filename': 'hellooooo.txt', 'extension': '.txt', 'file_size': 17, 'modified_time': 1752862258.4443583, 'format': 'text', 'lines': 1, 'words': 3, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 14}
2025-07-18 23:10:58,825 - database - INFO - Added 1 chunks for document: hellooooo.txt
2025-07-18 23:10:58,825 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (0 chunks)
2025-07-18 23:10:59,385 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:10:59,389 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (trigger: modified)
2025-07-18 23:10:59,421 - main - INFO - Search for 'sss' returned 7 results
2025-07-18 23:11:00,060 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:11:00,061 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:11:00,062 - document_processor - INFO - Processed hellooooo.txt: 1 chunks created
2025-07-18 23:11:00,062 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt: 1 chunks, type: <class 'list'>
2025-07-18 23:11:00,063 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:11:00,063 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 23:11:00,063 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:11:00,079 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\hellooooo.txt', 'filename': 'hellooooo.txt', 'extension': '.txt', 'file_size': 17, 'modified_time': 1752862259.3848307, 'format': 'text', 'lines': 1, 'words': 3, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 14}
2025-07-18 23:11:00,087 - database - INFO - Added 1 chunks for document: hellooooo.txt
2025-07-18 23:11:00,088 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt (0 chunks)
2025-07-18 23:11:09,958 - folder_manager - INFO - Removed file from store: E:\PROJECT\Semantic_Search_Assistant\test_docs\hellooooo.txt
2025-07-18 23:11:14,087 - main - INFO - Search for 'sssc' returned 8 results
2025-07-18 23:11:14,851 - main - INFO - Search for 'ssscc' returned 9 results
2025-07-18 23:11:14,984 - database - INFO - Vector store closed
2025-07-18 23:11:14,984 - api_service - INFO - API server shutdown complete
2025-07-18 23:11:14,985 - __main__ - INFO - Received signal 2, shutting down...
2025-07-18 23:11:15,058 - folder_manager - INFO - Background processor stopped
2025-07-18 23:11:59,265 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 23:12:09,917 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 23:12:09,946 - main - INFO - Initializing document search backend...
2025-07-18 23:12:09,947 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 23:12:09,949 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 23:12:09,949 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 23:12:15,641 - database - INFO - Created new table: documents
2025-07-18 23:12:15,642 - database - INFO - Vector store initialized successfully
2025-07-18 23:12:15,643 - document_processor - INFO - Document processor initialized
2025-07-18 23:12:15,726 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 23:12:15,726 - folder_manager - INFO - Starting folder monitoring...
2025-07-18 23:12:15,727 - folder_manager - WARNING - Folder does not exist, skipping monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-18 23:12:15,728 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 23:12:15,728 - folder_manager - INFO - Monitoring 2 folders
2025-07-18 23:12:15,731 - api_service - INFO - API server started successfully
2025-07-18 23:12:15,755 - folder_manager - INFO - Starting background document processor...
2025-07-18 23:12:24,984 - database - INFO - No documents in vector store yet
2025-07-18 23:12:24,984 - main - INFO - Search for 'h' returned 0 results
2025-07-18 23:12:25,100 - database - INFO - No documents in vector store yet
2025-07-18 23:12:25,100 - main - INFO - Search for 'he' returned 0 results
2025-07-18 23:12:26,166 - database - INFO - No documents in vector store yet
2025-07-18 23:12:26,166 - main - INFO - Search for 'h' returned 0 results
2025-07-18 23:12:31,623 - database - INFO - No documents in vector store yet
2025-07-18 23:12:31,629 - main - INFO - Search for 'c' returned 0 results
2025-07-18 23:12:38,954 - folder_manager - INFO - File created detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:12:38,958 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt (trigger: created)
2025-07-18 23:12:39,191 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:12:39,192 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:12:39,193 - document_processor - INFO - Processed New Text Document.txt: 0 chunks created
2025-07-18 23:12:39,193 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt: 0 chunks, type: <class 'list'>
2025-07-18 23:12:39,193 - database - INFO - add_document called with 0 chunks, type: <class 'list'>
2025-07-18 23:12:39,193 - database - INFO - No chunks to add for E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:12:39,193 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt (0 chunks)
2025-07-18 23:12:43,929 - database - INFO - No documents in vector store yet
2025-07-18 23:12:43,930 - main - INFO - Search for 'cc' returned 0 results
2025-07-18 23:12:44,061 - database - INFO - No documents in vector store yet
2025-07-18 23:12:44,061 - main - INFO - Search for 'cch' returned 0 results
2025-07-18 23:12:44,158 - database - INFO - No documents in vector store yet
2025-07-18 23:12:44,158 - main - INFO - Search for 'ccha' returned 0 results
2025-07-18 23:12:44,394 - database - INFO - No documents in vector store yet
2025-07-18 23:12:44,394 - main - INFO - Search for 'cchap' returned 0 results
2025-07-18 23:12:44,746 - database - INFO - No documents in vector store yet
2025-07-18 23:12:44,747 - main - INFO - Search for 'cchapt' returned 0 results
2025-07-18 23:12:44,857 - database - INFO - No documents in vector store yet
2025-07-18 23:12:44,858 - main - INFO - Search for 'cchapte' returned 0 results
2025-07-18 23:12:44,940 - database - INFO - No documents in vector store yet
2025-07-18 23:12:44,940 - main - INFO - Search for 'cchapter' returned 0 results
2025-07-18 23:12:45,247 - database - INFO - No documents in vector store yet
2025-07-18 23:12:45,248 - main - INFO - Search for 'cchapter1' returned 0 results
2025-07-18 23:12:48,446 - database - INFO - No documents in vector store yet
2025-07-18 23:12:48,446 - main - INFO - Search for 'cchapter1v' returned 0 results
2025-07-18 23:12:49,096 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter1.txt
2025-07-18 23:12:49,096 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter1.txt (trigger: modified)
2025-07-18 23:12:49,102 - database - INFO - No documents in vector store yet
2025-07-18 23:12:49,103 - main - INFO - Search for 'cchapter1vs' returned 0 results
2025-07-18 23:12:50,240 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter1.txt
2025-07-18 23:12:50,241 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter1.txt
2025-07-18 23:12:50,244 - document_processor - INFO - Processed chapter1.txt: 1 chunks created
2025-07-18 23:12:50,244 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter1.txt: 1 chunks, type: <class 'list'>
2025-07-18 23:12:50,244 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:12:50,245 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 23:12:50,245 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:12:50,328 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\chapter1.txt', 'filename': 'chapter1.txt', 'extension': '.txt', 'file_size': 402, 'modified_time': 1752862369.094158, 'format': 'text', 'lines': 2, 'words': 68, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 401}
2025-07-18 23:12:50,336 - database - INFO - Added 1 chunks for document: chapter1.txt
2025-07-18 23:12:50,336 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter1.txt (0 chunks)
2025-07-18 23:13:05,362 - main - INFO - Search for 'cchapter1v' returned 1 results
2025-07-18 23:13:05,710 - main - INFO - Search for 'cchapter1' returned 1 results
2025-07-18 23:13:05,826 - main - INFO - Search for 'cchapter' returned 1 results
2025-07-18 23:13:06,056 - main - INFO - Search for 'cchapte' returned 1 results
2025-07-18 23:13:06,566 - main - INFO - Search for 'cchapt' returned 1 results
2025-07-18 23:13:06,594 - main - INFO - Search for 'cchap' returned 1 results
2025-07-18 23:13:06,626 - main - INFO - Search for 'ccha' returned 1 results
2025-07-18 23:13:06,659 - main - INFO - Search for 'cch' returned 1 results
2025-07-18 23:13:06,693 - main - INFO - Search for 'cc' returned 1 results
2025-07-18 23:13:06,722 - main - INFO - Search for 'c' returned 1 results
2025-07-18 23:13:07,743 - main - INFO - Search for 'N' returned 1 results
2025-07-18 23:13:07,869 - main - INFO - Search for 'Na' returned 1 results
2025-07-18 23:13:08,084 - main - INFO - Search for 'Nat' returned 1 results
2025-07-18 23:13:08,201 - main - INFO - Search for 'Natu' returned 1 results
2025-07-18 23:13:08,357 - main - INFO - Search for 'Natur' returned 1 results
2025-07-18 23:13:08,472 - main - INFO - Search for 'Nature' returned 1 results
2025-07-18 23:13:10,641 - main - INFO - Search for 'Natur' returned 1 results
2025-07-18 23:13:10,789 - main - INFO - Search for 'Natu' returned 1 results
2025-07-18 23:13:10,925 - main - INFO - Search for 'Nat' returned 1 results
2025-07-18 23:13:11,045 - main - INFO - Search for 'Na' returned 1 results
2025-07-18 23:13:11,174 - main - INFO - Search for 'N' returned 1 results
2025-07-18 23:13:13,686 - main - INFO - Search for 'w' returned 1 results
2025-07-18 23:13:13,835 - main - INFO - Search for 'wo' returned 1 results
2025-07-18 23:13:14,040 - main - INFO - Search for 'wor' returned 1 results
2025-07-18 23:13:14,404 - main - INFO - Search for 'worl' returned 1 results
2025-07-18 23:13:14,603 - main - INFO - Search for 'world' returned 1 results
2025-07-18 23:13:16,585 - main - INFO - Search for 'worl' returned 1 results
2025-07-18 23:13:16,790 - main - INFO - Search for 'wor' returned 1 results
2025-07-18 23:13:16,934 - main - INFO - Search for 'wo' returned 1 results
2025-07-18 23:13:17,073 - main - INFO - Search for 'w' returned 1 results
2025-07-18 23:13:25,215 - main - INFO - Search for 'c' returned 1 results
2025-07-18 23:14:21,096 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 23:14:31,862 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 23:14:31,870 - main - INFO - Initializing document search backend...
2025-07-18 23:14:31,870 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 23:14:31,872 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 23:14:31,872 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 23:14:37,760 - database - INFO - Connected to existing table: documents
2025-07-18 23:14:37,760 - database - INFO - Vector store initialized successfully
2025-07-18 23:14:37,761 - document_processor - INFO - Document processor initialized
2025-07-18 23:14:37,892 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 23:14:37,893 - folder_manager - INFO - Starting folder monitoring...
2025-07-18 23:14:37,894 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 23:14:37,894 - folder_manager - WARNING - Folder does not exist, skipping monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-18 23:14:37,895 - folder_manager - INFO - Monitoring 2 folders
2025-07-18 23:14:37,921 - api_service - INFO - API server started successfully
2025-07-18 23:14:37,930 - folder_manager - INFO - Starting background document processor...
2025-07-18 23:14:53,506 - main - INFO - Search for 'w' returned 1 results
2025-07-18 23:14:53,513 - main - INFO - Search for 'wo' returned 1 results
2025-07-18 23:14:53,583 - main - INFO - Search for 'wor' returned 1 results
2025-07-18 23:14:53,715 - main - INFO - Search for 'word' returned 1 results
2025-07-18 23:14:59,949 - main - INFO - Search for 'wor' returned 1 results
2025-07-18 23:14:59,986 - main - INFO - Search for 'wo' returned 1 results
2025-07-18 23:15:00,060 - main - INFO - Search for 'w' returned 1 results
2025-07-18 23:15:03,784 - main - INFO - Search for 'n' returned 1 results
2025-07-18 23:15:03,870 - main - INFO - Search for 'na' returned 1 results
2025-07-18 23:15:05,047 - main - INFO - Search for 'nat' returned 1 results
2025-07-18 23:15:05,213 - main - INFO - Search for 'natu' returned 1 results
2025-07-18 23:15:05,383 - main - INFO - Search for 'natur' returned 1 results
2025-07-18 23:15:05,464 - main - INFO - Search for 'nature' returned 1 results
2025-07-18 23:15:10,133 - main - INFO - Search for 'natur' returned 1 results
2025-07-18 23:15:10,470 - main - INFO - Search for 'natu' returned 1 results
2025-07-18 23:15:10,595 - main - INFO - Search for 'nat' returned 1 results
2025-07-18 23:15:10,709 - main - INFO - Search for 'na' returned 1 results
2025-07-18 23:15:10,847 - main - INFO - Search for 'n' returned 1 results
2025-07-18 23:15:11,449 - main - INFO - Search for 'v' returned 1 results
2025-07-18 23:15:25,266 - main - INFO - Search for 'c' returned 1 results
2025-07-18 23:15:32,694 - folder_manager - INFO - File created detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:15:32,728 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt (trigger: created)
2025-07-18 23:15:33,602 - main - INFO - Search for 'c' returned 1 results
2025-07-18 23:15:33,708 - main - INFO - Search for 'ch' returned 1 results
2025-07-18 23:15:33,770 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:15:33,770 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:15:33,771 - document_processor - INFO - Processed New Text Document.txt: 0 chunks created
2025-07-18 23:15:33,771 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt: 0 chunks, type: <class 'list'>
2025-07-18 23:15:33,771 - database - INFO - add_document called with 0 chunks, type: <class 'list'>
2025-07-18 23:15:33,772 - database - INFO - No chunks to add for E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:15:33,772 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt (0 chunks)
2025-07-18 23:15:33,807 - main - INFO - Search for 'cha' returned 1 results
2025-07-18 23:15:34,001 - main - INFO - Search for 'chap' returned 1 results
2025-07-18 23:15:34,316 - main - INFO - Search for 'chapt' returned 1 results
2025-07-18 23:15:34,397 - main - INFO - Search for 'chapte' returned 1 results
2025-07-18 23:15:34,455 - main - INFO - Search for 'chapter' returned 1 results
2025-07-18 23:15:34,999 - main - INFO - Search for 'chapter2' returned 1 results
2025-07-18 23:15:39,124 - main - INFO - Search for 'chapter2v' returned 1 results
2025-07-18 23:15:40,017 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter2.txt
2025-07-18 23:15:40,022 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter2.txt (trigger: modified)
2025-07-18 23:15:40,101 - main - INFO - Search for 'chapter2vs' returned 1 results
2025-07-18 23:15:41,208 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter2.txt
2025-07-18 23:15:41,209 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter2.txt
2025-07-18 23:15:41,211 - document_processor - INFO - Processed chapter2.txt: 2 chunks created
2025-07-18 23:15:41,211 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter2.txt: 2 chunks, type: <class 'list'>
2025-07-18 23:15:41,211 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:15:41,211 - database - INFO - add_document called with 2 chunks, type: <class 'list'>
2025-07-18 23:15:41,211 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:15:41,329 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\chapter2.txt', 'filename': 'chapter2.txt', 'extension': '.txt', 'file_size': 766, 'modified_time': 1752862540.0157444, 'format': 'text', 'lines': 6, 'words': 123, 'chunk_index': 0, 'total_chunks': 2, 'chunk_size': 380}
2025-07-18 23:15:41,330 - database - INFO - Processing chunk 1: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\chapter2.txt', 'filename': 'chapter2.txt', 'extension': '.txt', 'file_size': 766, 'modified_time': 1752862540.0157444, 'format': 'text', 'lines': 6, 'words': 123, 'chunk_index': 1, 'total_chunks': 2, 'chunk_size': 376}
2025-07-18 23:15:41,353 - database - INFO - Added 2 chunks for document: chapter2.txt
2025-07-18 23:15:41,354 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter2.txt (0 chunks)
2025-07-18 23:15:44,836 - main - INFO - Search for 'chapter2v' returned 3 results
2025-07-18 23:15:45,017 - main - INFO - Search for 'chapter2' returned 3 results
2025-07-18 23:15:45,180 - main - INFO - Search for 'chapter' returned 3 results
2025-07-18 23:15:45,638 - main - INFO - Search for 'chapte' returned 3 results
2025-07-18 23:15:45,696 - main - INFO - Search for 'chapt' returned 3 results
2025-07-18 23:15:45,776 - main - INFO - Search for 'chap' returned 3 results
2025-07-18 23:15:45,826 - main - INFO - Search for 'cha' returned 3 results
2025-07-18 23:15:45,852 - main - INFO - Search for 'ch' returned 3 results
2025-07-18 23:15:45,871 - main - INFO - Search for 'c' returned 3 results
2025-07-18 23:19:13,624 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 23:19:26,236 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 23:19:26,262 - main - INFO - Initializing document search backend...
2025-07-18 23:19:26,263 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 23:19:26,274 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 23:19:26,334 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 23:19:32,286 - database - INFO - Connected to existing table: documents
2025-07-18 23:19:32,287 - database - INFO - Vector store initialized successfully
2025-07-18 23:19:32,287 - document_processor - INFO - Document processor initialized
2025-07-18 23:19:32,411 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 23:19:32,411 - folder_manager - INFO - Starting folder monitoring...
2025-07-18 23:19:32,413 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 23:19:32,414 - folder_manager - WARNING - Folder does not exist, skipping monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-18 23:19:32,417 - folder_manager - INFO - Monitoring 2 folders
2025-07-18 23:19:32,420 - api_service - INFO - API server started successfully
2025-07-18 23:19:32,433 - folder_manager - INFO - Starting background document processor...
2025-07-18 23:19:43,432 - main - INFO - Search for 'w' returned 3 results
2025-07-18 23:19:43,456 - main - INFO - Search for 'wo' returned 3 results
2025-07-18 23:19:43,517 - main - INFO - Search for 'wor' returned 3 results
2025-07-18 23:19:43,648 - main - INFO - Search for 'word' returned 3 results
2025-07-18 23:19:49,685 - main - INFO - Search for 'wor' returned 3 results
2025-07-18 23:19:50,204 - main - INFO - Search for 'wo' returned 3 results
2025-07-18 23:19:50,241 - main - INFO - Search for 'w' returned 3 results
2025-07-18 23:19:50,933 - main - INFO - Search for 'n' returned 3 results
2025-07-18 23:19:50,984 - main - INFO - Search for 'na' returned 3 results
2025-07-18 23:19:51,191 - main - INFO - Search for 'nat' returned 3 results
2025-07-18 23:19:51,377 - main - INFO - Search for 'natu' returned 3 results
2025-07-18 23:19:51,521 - main - INFO - Search for 'natur' returned 3 results
2025-07-18 23:19:51,620 - main - INFO - Search for 'nature' returned 3 results
2025-07-18 23:19:52,671 - main - INFO - Search for 'natur' returned 3 results
2025-07-18 23:19:52,813 - main - INFO - Search for 'natu' returned 3 results
2025-07-18 23:19:52,935 - main - INFO - Search for 'nat' returned 3 results
2025-07-18 23:19:53,049 - main - INFO - Search for 'na' returned 3 results
2025-07-18 23:19:53,177 - main - INFO - Search for 'n' returned 3 results
2025-07-18 23:19:53,669 - main - INFO - Search for 't' returned 3 results
2025-07-18 23:19:53,754 - main - INFO - Search for 'te' returned 3 results
2025-07-18 23:19:53,961 - main - INFO - Search for 'tec' returned 3 results
2025-07-18 23:19:54,099 - main - INFO - Search for 'tech' returned 3 results
2025-07-18 23:19:54,311 - main - INFO - Search for 'techn' returned 3 results
2025-07-18 23:19:54,460 - main - INFO - Search for 'techno' returned 3 results
2025-07-18 23:19:55,200 - main - INFO - Search for 'technol' returned 3 results
2025-07-18 23:19:55,344 - main - INFO - Search for 'technolo' returned 3 results
2025-07-18 23:19:55,533 - main - INFO - Search for 'technolog' returned 3 results
2025-07-18 23:19:55,683 - main - INFO - Search for 'technology' returned 3 results
2025-07-18 23:19:56,729 - main - INFO - Search for 'technolog' returned 3 results
2025-07-18 23:19:57,247 - main - INFO - Search for 'technolo' returned 3 results
2025-07-18 23:19:57,292 - main - INFO - Search for 'technol' returned 3 results
2025-07-18 23:19:57,304 - main - INFO - Search for 'techno' returned 3 results
2025-07-18 23:19:57,348 - main - INFO - Search for 'techn' returned 3 results
2025-07-18 23:19:57,368 - main - INFO - Search for 'tech' returned 3 results
2025-07-18 23:19:57,409 - main - INFO - Search for 'tec' returned 3 results
2025-07-18 23:19:57,443 - main - INFO - Search for 'te' returned 3 results
2025-07-18 23:19:57,463 - main - INFO - Search for 't' returned 3 results
2025-07-18 23:19:58,126 - main - INFO - Search for 'f' returned 3 results
2025-07-18 23:19:58,331 - main - INFO - Search for 'fc' returned 3 results
2025-07-18 23:19:58,595 - main - INFO - Search for 'fct' returned 3 results
2025-07-18 23:19:58,677 - main - INFO - Search for 'fcti' returned 3 results
2025-07-18 23:19:58,757 - main - INFO - Search for 'fctio' returned 3 results
2025-07-18 23:19:58,960 - main - INFO - Search for 'fction' returned 3 results
2025-07-18 23:19:59,075 - main - INFO - Search for 'fctiona' returned 3 results
2025-07-18 23:19:59,159 - main - INFO - Search for 'fctional' returned 3 results
2025-07-18 23:19:59,873 - main - INFO - Search for 'fctiona' returned 3 results
2025-07-18 23:20:00,353 - main - INFO - Search for 'fction' returned 3 results
2025-07-18 23:20:00,530 - main - INFO - Search for 'fctio' returned 3 results
2025-07-18 23:20:00,583 - main - INFO - Search for 'fcti' returned 3 results
2025-07-18 23:20:00,640 - main - INFO - Search for 'fct' returned 3 results
2025-07-18 23:20:00,656 - main - INFO - Search for 'fc' returned 3 results
2025-07-18 23:20:00,673 - main - INFO - Search for 'f' returned 3 results
2025-07-18 23:20:01,187 - main - INFO - Search for 'f' returned 3 results
2025-07-18 23:20:01,265 - main - INFO - Search for 'fi' returned 3 results
2025-07-18 23:20:01,547 - main - INFO - Search for 'fic' returned 3 results
2025-07-18 23:20:02,124 - main - INFO - Search for 'fict' returned 3 results
2025-07-18 23:20:02,536 - main - INFO - Search for 'ficti' returned 3 results
2025-07-18 23:20:02,626 - main - INFO - Search for 'fictio' returned 3 results
2025-07-18 23:20:03,253 - main - INFO - Search for 'fiction' returned 3 results
2025-07-18 23:20:03,559 - main - INFO - Search for 'fictiona' returned 3 results
2025-07-18 23:20:03,905 - main - INFO - Search for 'fictional' returned 3 results
2025-07-18 23:20:04,628 - main - INFO - Search for 's' returned 3 results
2025-07-18 23:20:04,861 - main - INFO - Search for 'st' returned 3 results
2025-07-18 23:20:04,994 - main - INFO - Search for 'sto' returned 3 results
2025-07-18 23:20:05,260 - main - INFO - Search for 'stor' returned 3 results
2025-07-18 23:20:05,384 - main - INFO - Search for 'store' returned 3 results
2025-07-18 23:20:05,866 - main - INFO - Search for 'stor' returned 3 results
2025-07-18 23:20:07,613 - main - INFO - Search for 'sto' returned 3 results
2025-07-18 23:20:08,065 - main - INFO - Search for 'st' returned 3 results
2025-07-18 23:20:08,111 - main - INFO - Search for 's' returned 3 results
2025-07-18 23:20:08,730 - main - INFO - Search for 'r' returned 3 results
2025-07-18 23:20:08,884 - main - INFO - Search for 'ro' returned 3 results
2025-07-18 23:20:09,182 - main - INFO - Search for 'rom' returned 3 results
2025-07-18 23:20:09,622 - main - INFO - Search for 'ro' returned 3 results
2025-07-18 23:20:10,276 - main - INFO - Search for 'roo' returned 3 results
2025-07-18 23:20:10,493 - main - INFO - Search for 'room' returned 3 results
2025-07-18 23:20:14,164 - main - INFO - Search for 'roo' returned 3 results
2025-07-18 23:20:14,682 - main - INFO - Search for 'ro' returned 3 results
2025-07-18 23:20:14,702 - main - INFO - Search for 'r' returned 3 results
2025-07-18 23:20:15,298 - main - INFO - Search for 'n' returned 3 results
2025-07-18 23:20:15,354 - main - INFO - Search for 'na' returned 3 results
2025-07-18 23:20:15,620 - main - INFO - Search for 'nat' returned 3 results
2025-07-18 23:20:15,686 - main - INFO - Search for 'natu' returned 3 results
2025-07-18 23:20:15,817 - main - INFO - Search for 'natur' returned 3 results
2025-07-18 23:20:15,922 - main - INFO - Search for 'nature' returned 3 results
2025-07-18 23:20:20,344 - main - INFO - Search for 'natur' returned 3 results
2025-07-18 23:20:20,824 - main - INFO - Search for 'natu' returned 3 results
2025-07-18 23:20:20,879 - main - INFO - Search for 'nat' returned 3 results
2025-07-18 23:20:20,953 - main - INFO - Search for 'na' returned 3 results
2025-07-18 23:20:21,020 - main - INFO - Search for 'n' returned 3 results
2025-07-18 23:20:22,484 - main - INFO - Search for 'v' returned 3 results
2025-07-18 23:20:34,473 - folder_manager - INFO - File created detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:20:34,480 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt (trigger: created)
2025-07-18 23:20:35,308 - main - INFO - Search for 'c' returned 3 results
2025-07-18 23:20:35,442 - main - INFO - Search for 'ch' returned 3 results
2025-07-18 23:20:35,516 - main - INFO - Search for 'cha' returned 3 results
2025-07-18 23:20:35,517 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:20:35,520 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:20:35,521 - document_processor - INFO - Processed New Text Document.txt: 0 chunks created
2025-07-18 23:20:35,522 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt: 0 chunks, type: <class 'list'>
2025-07-18 23:20:35,522 - database - INFO - add_document called with 0 chunks, type: <class 'list'>
2025-07-18 23:20:35,522 - database - INFO - No chunks to add for E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt
2025-07-18 23:20:35,522 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\New Text Document.txt (0 chunks)
2025-07-18 23:20:35,707 - main - INFO - Search for 'chap' returned 3 results
2025-07-18 23:20:36,001 - main - INFO - Search for 'chapt' returned 3 results
2025-07-18 23:20:36,089 - main - INFO - Search for 'chapte' returned 3 results
2025-07-18 23:20:36,128 - main - INFO - Search for 'chapter' returned 3 results
2025-07-18 23:20:36,437 - main - INFO - Search for 'chapter3' returned 3 results
2025-07-18 23:20:48,382 - main - INFO - Search for 'chapter3c' returned 3 results
2025-07-18 23:20:50,414 - main - INFO - Search for 'chapter3cv' returned 3 results
2025-07-18 23:20:51,105 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter3.txt
2025-07-18 23:20:51,106 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter3.txt (trigger: modified)
2025-07-18 23:20:51,145 - main - INFO - Search for 'chapter3cvs' returned 3 results
2025-07-18 23:20:51,569 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter3.txt
2025-07-18 23:20:51,570 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter3.txt
2025-07-18 23:20:51,572 - document_processor - INFO - Processed chapter3.txt: 1 chunks created
2025-07-18 23:20:51,573 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter3.txt: 1 chunks, type: <class 'list'>
2025-07-18 23:20:51,573 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 23:20:51,574 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 23:20:51,574 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 23:20:51,630 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\chapter3.txt', 'filename': 'chapter3.txt', 'extension': '.txt', 'file_size': 406, 'modified_time': 1752862851.1043897, 'format': 'text', 'lines': 2, 'words': 74, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 405}
2025-07-18 23:20:51,639 - database - INFO - Added 1 chunks for document: chapter3.txt
2025-07-18 23:20:51,640 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\chapter3.txt (0 chunks)
2025-07-18 23:20:55,819 - main - INFO - Search for 'chapter3cv' returned 4 results
2025-07-18 23:20:56,325 - main - INFO - Search for 'chapter3c' returned 4 results
2025-07-18 23:20:56,375 - main - INFO - Search for 'chapter3' returned 4 results
2025-07-18 23:20:56,403 - main - INFO - Search for 'chapter' returned 4 results
2025-07-18 23:20:56,432 - main - INFO - Search for 'chapte' returned 4 results
2025-07-18 23:20:56,471 - main - INFO - Search for 'chapt' returned 4 results
2025-07-18 23:20:56,504 - main - INFO - Search for 'chap' returned 4 results
2025-07-18 23:20:56,533 - main - INFO - Search for 'cha' returned 4 results
2025-07-18 23:20:56,557 - main - INFO - Search for 'ch' returned 4 results
2025-07-18 23:20:56,592 - main - INFO - Search for 'c' returned 4 results
2025-07-18 23:21:03,622 - main - INFO - Search for 's' returned 4 results
2025-07-18 23:21:04,164 - main - INFO - Search for 'sl' returned 4 results
2025-07-18 23:21:04,421 - main - INFO - Search for 'sli' returned 4 results
2025-07-18 23:21:04,862 - main - INFO - Search for 'slic' returned 4 results
2025-07-18 23:21:05,070 - main - INFO - Search for 'slice' returned 4 results
2025-07-18 23:21:05,987 - main - INFO - Search for 'o' returned 4 results
2025-07-18 23:21:06,745 - main - INFO - Search for 'of' returned 4 results
2025-07-18 23:21:07,153 - main - INFO - Search for 'o' returned 4 results
2025-07-18 23:21:09,437 - main - INFO - Search for 's' returned 4 results
2025-07-18 23:21:09,623 - main - INFO - Search for 'sl' returned 4 results
2025-07-18 23:21:09,782 - main - INFO - Search for 'sli' returned 4 results
2025-07-18 23:21:09,872 - main - INFO - Search for 'slic' returned 4 results
2025-07-18 23:21:09,995 - main - INFO - Search for 'slice' returned 4 results
2025-07-18 23:21:13,791 - main - INFO - Search for 'slicev' returned 4 results
2025-07-18 23:21:16,855 - main - INFO - Search for 'slice' returned 4 results
2025-07-18 23:21:17,010 - main - INFO - Search for 'slic' returned 4 results
2025-07-18 23:21:17,377 - main - INFO - Search for 'sli' returned 4 results
2025-07-18 23:21:17,530 - main - INFO - Search for 'sl' returned 4 results
2025-07-18 23:21:17,657 - main - INFO - Search for 's' returned 4 results
2025-07-18 23:21:24,655 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 23:21:37,759 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 23:21:37,775 - main - INFO - Initializing document search backend...
2025-07-18 23:21:37,775 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 23:21:37,780 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 23:21:37,780 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 16:44:05,271 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-30 16:44:39,542 - datasets - INFO - PyTorch version 2.4.0 available.
2025-07-30 16:44:39,572 - datasets - INFO - JAX version 0.5.2 available.
2025-07-30 16:44:41,297 - folder_manager - INFO - Loaded 2 connected folders
2025-07-30 16:44:41,527 - main - INFO - Initializing document search backend...
2025-07-30 16:44:41,528 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 16:44:41,531 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-30 16:44:41,532 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 16:44:47,508 - database - INFO - Connected to existing table: documents
2025-07-30 16:44:47,508 - database - INFO - Vector store initialized successfully
2025-07-30 16:44:47,511 - document_processor - INFO - Document processor initialized
2025-07-30 16:44:47,665 - main - INFO - Added test_docs folder to monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 16:44:47,666 - folder_manager - INFO - Starting folder monitoring...
2025-07-30 16:44:47,669 - folder_manager - INFO - Started monitoring folder: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 16:44:47,670 - folder_manager - WARNING - Folder does not exist, skipping monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-30 16:44:47,670 - folder_manager - INFO - Monitoring 2 folders
2025-07-30 16:44:47,682 - api_service - INFO - API server started successfully
2025-07-30 16:44:47,715 - folder_manager - INFO - Starting background document processor...
2025-07-30 16:46:12,020 - main - INFO - Search for 'b' returned 4 results
2025-07-30 16:46:12,064 - main - INFO - Search for 'bi' returned 4 results
2025-07-30 16:46:12,400 - main - INFO - Search for 'bil' returned 4 results
2025-07-30 16:46:12,574 - main - INFO - Search for 'bila' returned 4 results
2025-07-30 16:46:12,731 - main - INFO - Search for 'bilal' returned 4 results
2025-07-30 16:46:35,064 - main - INFO - Search for 'bilalr' returned 4 results
2025-07-30 16:46:35,158 - main - INFO - Search for 'bilalri' returned 4 results
2025-07-30 16:46:35,353 - main - INFO - Search for 'bilalrig' returned 4 results
2025-07-30 16:46:35,435 - main - INFO - Search for 'bilalrigh' returned 4 results
2025-07-30 16:46:35,530 - main - INFO - Search for 'bilalright' returned 4 results
2025-07-30 16:46:35,818 - main - INFO - Search for 'n' returned 4 results
2025-07-30 16:46:35,909 - main - INFO - Search for 'no' returned 4 results
2025-07-30 16:46:37,025 - main - INFO - Search for 'now' returned 4 results
2025-07-30 16:46:37,682 - main - INFO - Search for 'i' returned 4 results
2025-07-30 16:46:37,796 - main - INFO - Search for 'in' returned 4 results
2025-07-30 17:11:04,398 - database - INFO - Connected to existing table: documents
2025-07-30 17:11:04,399 - database - INFO - Vector store initialized successfully
2025-07-30 17:11:04,401 - document_processor - INFO - Document processor initialized
2025-07-30 17:11:04,563 - main - INFO - Added test_docs folder to monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:11:04,569 - folder_manager - INFO - Starting folder monitoring...
2025-07-30 17:11:04,569 - folder_manager - WARNING - Folder does not exist, skipping monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-30 17:11:04,573 - folder_manager - INFO - Started monitoring folder: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:11:04,574 - folder_manager - INFO - Monitoring 2 folders
2025-07-30 17:11:04,586 - api_service - INFO - API server started successfully
2025-07-30 17:11:04,618 - folder_manager - INFO - Starting background document processor...
2025-07-30 17:11:04,623 - database - INFO - Vector store closed
2025-07-30 17:11:04,624 - api_service - INFO - API server shutdown complete
2025-07-30 17:11:04,718 - folder_manager - INFO - Background processor stopped
2025-07-30 17:12:29,567 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-30 17:12:41,337 - datasets - INFO - PyTorch version 2.4.0 available.
2025-07-30 17:12:41,345 - datasets - INFO - JAX version 0.5.2 available.
2025-07-30 17:12:44,249 - folder_manager - INFO - Loaded 2 connected folders
2025-07-30 17:12:44,320 - main - INFO - Initializing document search backend...
2025-07-30 17:12:44,320 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:12:44,328 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-30 17:12:44,329 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:12:49,479 - database - INFO - Connected to existing table: documents
2025-07-30 17:12:49,480 - database - INFO - Vector store initialized successfully
2025-07-30 17:12:49,481 - document_processor - INFO - Document processor initialized
2025-07-30 17:12:49,623 - main - INFO - Added test_docs folder to monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:12:49,623 - folder_manager - INFO - Starting folder monitoring...
2025-07-30 17:12:49,632 - folder_manager - INFO - Started monitoring folder: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:12:49,633 - folder_manager - WARNING - Folder does not exist, skipping monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-30 17:12:49,633 - folder_manager - INFO - Monitoring 2 folders
2025-07-30 17:12:49,640 - api_service - INFO - API server started successfully
2025-07-30 17:12:49,682 - folder_manager - INFO - Starting background document processor...
2025-07-30 17:13:02,176 - main - INFO - Search for 'j' returned 4 results
2025-07-30 17:13:02,315 - main - INFO - Search for 'je' returned 4 results
2025-07-30 17:13:02,606 - main - INFO - Search for 'jel' returned 4 results
2025-07-30 17:13:02,775 - main - INFO - Search for 'jell' returned 4 results
2025-07-30 17:13:07,529 - main - INFO - Search for 'jel' returned 4 results
2025-07-30 17:13:08,060 - main - INFO - Search for 'je' returned 4 results
2025-07-30 17:13:08,081 - main - INFO - Search for 'j' returned 4 results
2025-07-30 17:13:13,697 - main - INFO - Search for 'j' returned 4 results
2025-07-30 17:13:14,007 - main - INFO - Search for 'ja' returned 4 results
2025-07-30 17:13:15,544 - main - INFO - Search for 'j' returned 4 results
2025-07-30 17:13:16,343 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:13:16,537 - main - INFO - Search for 'bi' returned 4 results
2025-07-30 17:13:16,738 - main - INFO - Search for 'bil' returned 4 results
2025-07-30 17:13:16,897 - main - INFO - Search for 'bila' returned 4 results
2025-07-30 17:13:17,040 - main - INFO - Search for 'bilal' returned 4 results
2025-07-30 17:13:27,033 - main - INFO - Search for 'bilala' returned 4 results
2025-07-30 17:13:27,827 - main - INFO - Search for 'bilalac' returned 4 results
2025-07-30 17:13:28,208 - main - INFO - Search for 'bilalacc' returned 4 results
2025-07-30 17:13:36,023 - main - INFO - Search for 'bilalac' returned 4 results
2025-07-30 17:13:37,427 - main - INFO - Search for 'bilalacv' returned 4 results
2025-07-30 17:13:43,391 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:13:43,517 - main - INFO - Search for 'wh' returned 4 results
2025-07-30 17:13:43,730 - main - INFO - Search for 'whe' returned 4 results
2025-07-30 17:13:43,741 - main - INFO - Search for 'when' returned 4 results
2025-07-30 17:13:44,009 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:13:44,475 - main - INFO - Search for 'h' returned 4 results
2025-07-30 17:13:44,581 - main - INFO - Search for 'ho' returned 4 results
2025-07-30 17:13:44,734 - main - INFO - Search for 'hov' returned 4 results
2025-07-30 17:13:44,850 - main - INFO - Search for 'hove' returned 4 results
2025-07-30 17:13:44,947 - main - INFO - Search for 'hover' returned 4 results
2025-07-30 17:13:45,253 - main - INFO - Search for 'o' returned 4 results
2025-07-30 17:13:45,408 - main - INFO - Search for 'ov' returned 4 results
2025-07-30 17:13:45,508 - main - INFO - Search for 'ove' returned 4 results
2025-07-30 17:13:45,625 - main - INFO - Search for 'over' returned 4 results
2025-07-30 17:13:46,051 - main - INFO - Search for 'r' returned 4 results
2025-07-30 17:13:46,138 - main - INFO - Search for 're' returned 4 results
2025-07-30 17:13:46,380 - main - INFO - Search for 'res' returned 4 results
2025-07-30 17:13:46,627 - main - INFO - Search for 'resl' returned 4 results
2025-07-30 17:13:47,123 - main - INFO - Search for 'res' returned 4 results
2025-07-30 17:13:48,808 - main - INFO - Search for 'resu' returned 4 results
2025-07-30 17:13:48,918 - main - INFO - Search for 'resul' returned 4 results
2025-07-30 17:13:49,087 - main - INFO - Search for 'result' returned 4 results
2025-07-30 17:13:49,154 - main - INFO - Search for 'results' returned 4 results
2025-07-30 17:13:49,600 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:13:49,819 - main - INFO - Search for 'it' returned 4 results
2025-07-30 17:13:50,040 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:13:50,183 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:13:50,247 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:13:50,382 - main - INFO - Search for 'shou' returned 4 results
2025-07-30 17:13:50,516 - main - INFO - Search for 'shoul' returned 4 results
2025-07-30 17:13:50,596 - main - INFO - Search for 'should' returned 4 results
2025-07-30 17:13:52,168 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:13:52,310 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:13:52,404 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:13:52,482 - main - INFO - Search for 'show' returned 4 results
2025-07-30 17:13:52,783 - main - INFO - Search for 'h' returned 4 results
2025-07-30 17:13:52,877 - main - INFO - Search for 'ha' returned 4 results
2025-07-30 17:13:53,001 - main - INFO - Search for 'han' returned 4 results
2025-07-30 17:13:53,112 - main - INFO - Search for 'hand' returned 4 results
2025-07-30 17:13:53,389 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:13:53,496 - main - INFO - Search for 'ic' returned 4 results
2025-07-30 17:13:53,653 - main - INFO - Search for 'ico' returned 4 results
2025-07-30 17:13:53,739 - main - INFO - Search for 'icon' returned 4 results
2025-07-30 17:13:55,227 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:13:55,342 - main - INFO - Search for 'wh' returned 4 results
2025-07-30 17:13:55,428 - main - INFO - Search for 'whi' returned 4 results
2025-07-30 17:13:55,530 - main - INFO - Search for 'whic' returned 4 results
2025-07-30 17:13:55,648 - main - INFO - Search for 'which' returned 4 results
2025-07-30 17:13:56,052 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:13:56,191 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:13:56,205 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:13:56,386 - main - INFO - Search for 'show' returned 4 results
2025-07-30 17:13:56,614 - main - INFO - Search for 'shown' returned 4 results
2025-07-30 17:13:57,077 - main - INFO - Search for 'show' returned 4 results
2025-07-30 17:13:57,220 - main - INFO - Search for 'shows' returned 4 results
2025-07-30 17:13:57,976 - main - INFO - Search for 'g' returned 4 results
2025-07-30 17:13:58,097 - main - INFO - Search for 'gr' returned 4 results
2025-07-30 17:13:58,266 - main - INFO - Search for 'gra' returned 4 results
2025-07-30 17:13:58,667 - main - INFO - Search for 'grab' returned 4 results
2025-07-30 17:14:01,732 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:14:01,934 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:14:02,041 - main - INFO - Search for 'and' returned 4 results
2025-07-30 17:14:03,568 - main - INFO - Search for 'o' returned 4 results
2025-07-30 17:14:03,822 - main - INFO - Search for 'oc' returned 4 results
2025-07-30 17:14:03,885 - main - INFO - Search for 'ocl' returned 4 results
2025-07-30 17:14:04,036 - main - INFO - Search for 'ocli' returned 4 results
2025-07-30 17:14:04,105 - main - INFO - Search for 'oclic' returned 4 results
2025-07-30 17:14:04,226 - main - INFO - Search for 'oclick' returned 4 results
2025-07-30 17:14:04,406 - main - INFO - Search for 'oclicki' returned 4 results
2025-07-30 17:14:04,504 - main - INFO - Search for 'oclickin' returned 4 results
2025-07-30 17:14:04,896 - main - INFO - Search for 'oclicki' returned 4 results
2025-07-30 17:14:05,384 - main - INFO - Search for 'oclick' returned 4 results
2025-07-30 17:14:05,426 - main - INFO - Search for 'oclic' returned 4 results
2025-07-30 17:14:05,470 - main - INFO - Search for 'ocli' returned 4 results
2025-07-30 17:14:05,622 - main - INFO - Search for 'ocl' returned 4 results
2025-07-30 17:14:05,789 - main - INFO - Search for 'oc' returned 4 results
2025-07-30 17:14:05,953 - main - INFO - Search for 'o' returned 4 results
2025-07-30 17:14:06,527 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:14:06,904 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:14:07,041 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:14:07,151 - main - INFO - Search for 'then' returned 4 results
2025-07-30 17:14:07,741 - main - INFO - Search for 'y' returned 4 results
2025-07-30 17:14:07,892 - main - INFO - Search for 'yo' returned 4 results
2025-07-30 17:14:08,003 - main - INFO - Search for 'you' returned 4 results
2025-07-30 17:14:08,304 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:14:08,410 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:14:08,485 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:14:08,586 - main - INFO - Search for 'shou' returned 4 results
2025-07-30 17:14:08,723 - main - INFO - Search for 'shoul' returned 4 results
2025-07-30 17:14:08,748 - main - INFO - Search for 'should' returned 4 results
2025-07-30 17:14:09,056 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:14:09,133 - main - INFO - Search for 'be' returned 4 results
2025-07-30 17:14:09,745 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:14:09,822 - main - INFO - Search for 'ab' returned 4 results
2025-07-30 17:14:09,952 - main - INFO - Search for 'abl' returned 4 results
2025-07-30 17:14:10,086 - main - INFO - Search for 'able' returned 4 results
2025-07-30 17:14:10,347 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:14:10,366 - main - INFO - Search for 'to' returned 4 results
2025-07-30 17:14:10,800 - main - INFO - Search for 'f' returned 4 results
2025-07-30 17:14:10,885 - main - INFO - Search for 'fr' returned 4 results
2025-07-30 17:14:11,191 - main - INFO - Search for 'f' returned 4 results
2025-07-30 17:14:11,367 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:14:11,533 - main - INFO - Search for 'dr' returned 4 results
2025-07-30 17:14:11,665 - main - INFO - Search for 'dra' returned 4 results
2025-07-30 17:14:11,841 - main - INFO - Search for 'drag' returned 4 results
2025-07-30 17:14:12,031 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:14:12,187 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:14:12,260 - main - INFO - Search for 'and' returned 4 results
2025-07-30 17:14:12,915 - main - INFO - Search for 'r' returned 4 results
2025-07-30 17:14:13,096 - main - INFO - Search for 'ro' returned 4 results
2025-07-30 17:14:13,222 - main - INFO - Search for 'rop' returned 4 results
2025-07-30 17:14:13,563 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:14:13,635 - main - INFO - Search for 'in' returned 4 results
2025-07-30 17:14:13,955 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:14:14,129 - main - INFO - Search for 'cr' returned 4 results
2025-07-30 17:14:14,285 - main - INFO - Search for 'cro' returned 4 results
2025-07-30 17:14:14,885 - main - INFO - Search for 'cros' returned 4 results
2025-07-30 17:14:15,030 - main - INFO - Search for 'cross' returned 4 results
2025-07-30 17:14:16,240 - main - INFO - Search for 'cros' returned 4 results
2025-07-30 17:14:16,451 - main - INFO - Search for 'cro' returned 4 results
2025-07-30 17:14:16,636 - main - INFO - Search for 'cr' returned 4 results
2025-07-30 17:14:16,801 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:14:17,284 - main - INFO - Search for 'e' returned 4 results
2025-07-30 17:14:17,467 - main - INFO - Search for 'ex' returned 4 results
2025-07-30 17:14:17,683 - main - INFO - Search for 'ext' returned 4 results
2025-07-30 17:14:17,885 - main - INFO - Search for 'exte' returned 4 results
2025-07-30 17:14:18,031 - main - INFO - Search for 'exter' returned 4 results
2025-07-30 17:14:18,404 - main - INFO - Search for 'extera' returned 4 results
2025-07-30 17:14:18,770 - main - INFO - Search for 'exteral' returned 4 results
2025-07-30 17:14:19,245 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:14:19,381 - main - INFO - Search for 'ap' returned 4 results
2025-07-30 17:14:19,512 - main - INFO - Search for 'app' returned 4 results
2025-07-30 17:14:19,746 - main - INFO - Search for 'appl' returned 4 results
2025-07-30 17:14:19,845 - main - INFO - Search for 'appli' returned 4 results
2025-07-30 17:14:19,987 - main - INFO - Search for 'applic' returned 4 results
2025-07-30 17:14:20,080 - main - INFO - Search for 'applica' returned 4 results
2025-07-30 17:14:20,357 - main - INFO - Search for 'applicat' returned 4 results
2025-07-30 17:14:20,440 - main - INFO - Search for 'applicati' returned 4 results
2025-07-30 17:14:20,541 - main - INFO - Search for 'applicatio' returned 4 results
2025-07-30 17:14:20,611 - main - INFO - Search for 'application' returned 4 results
2025-07-30 17:14:20,998 - main - INFO - Search for 'l' returned 4 results
2025-07-30 17:14:21,105 - main - INFO - Search for 'li' returned 4 results
2025-07-30 17:14:21,283 - main - INFO - Search for 'lik' returned 4 results
2025-07-30 17:14:21,351 - main - INFO - Search for 'like' returned 4 results
2025-07-30 17:14:21,686 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:14:21,801 - main - INFO - Search for 'wo' returned 4 results
2025-07-30 17:14:21,861 - main - INFO - Search for 'wor' returned 4 results
2025-07-30 17:14:22,121 - main - INFO - Search for 'word' returned 4 results
2025-07-30 17:14:22,454 - main - INFO - Search for 'e' returned 4 results
2025-07-30 17:14:22,645 - main - INFO - Search for 'et' returned 4 results
2025-07-30 17:14:22,892 - main - INFO - Search for 'etc' returned 4 results
2025-07-30 17:20:44,310 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-30 17:20:58,742 - datasets - INFO - PyTorch version 2.4.0 available.
2025-07-30 17:20:58,750 - datasets - INFO - JAX version 0.5.2 available.
2025-07-30 17:20:59,633 - folder_manager - INFO - Loaded 2 connected folders
2025-07-30 17:20:59,645 - main - INFO - Initializing document search backend...
2025-07-30 17:20:59,646 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:20:59,664 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-30 17:20:59,664 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:21:05,911 - database - INFO - Connected to existing table: documents
2025-07-30 17:21:05,911 - database - INFO - Vector store initialized successfully
2025-07-30 17:21:05,913 - document_processor - INFO - Document processor initialized
2025-07-30 17:21:06,065 - main - INFO - Added test_docs folder to monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:21:06,066 - folder_manager - INFO - Starting folder monitoring...
2025-07-30 17:21:06,069 - folder_manager - INFO - Started monitoring folder: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:21:06,070 - folder_manager - WARNING - Folder does not exist, skipping monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-30 17:21:06,070 - folder_manager - INFO - Monitoring 2 folders
2025-07-30 17:21:06,081 - api_service - INFO - API server started successfully
2025-07-30 17:21:06,118 - folder_manager - INFO - Starting background document processor...
2025-07-30 17:21:15,128 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:21:15,361 - main - INFO - Search for 'bi' returned 4 results
2025-07-30 17:21:15,720 - main - INFO - Search for 'bil' returned 4 results
2025-07-30 17:22:31,412 - main - INFO - Search for 'bili' returned 4 results
2025-07-30 17:22:31,755 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:22:31,878 - main - INFO - Search for 'ca' returned 4 results
2025-07-30 17:22:32,330 - main - INFO - Search for 'can' returned 4 results
2025-07-30 17:22:32,842 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:22:33,061 - main - INFO - Search for 'se' returned 4 results
2025-07-30 17:22:33,208 - main - INFO - Search for 'see' returned 4 results
2025-07-30 17:22:33,593 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:22:33,749 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:22:33,910 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:22:34,448 - main - INFO - Search for 'f' returned 4 results
2025-07-30 17:22:34,961 - main - INFO - Search for 'fi' returned 4 results
2025-07-30 17:22:35,045 - main - INFO - Search for 'fin' returned 4 results
2025-07-30 17:22:35,245 - main - INFO - Search for 'fing' returned 4 results
2025-07-30 17:22:35,325 - main - INFO - Search for 'finge' returned 4 results
2025-07-30 17:22:35,469 - main - INFO - Search for 'finger' returned 4 results
2025-07-30 17:22:35,879 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:22:36,006 - main - INFO - Search for 'ic' returned 4 results
2025-07-30 17:22:36,147 - main - INFO - Search for 'ico' returned 4 results
2025-07-30 17:22:36,261 - main - INFO - Search for 'icon' returned 4 results
2025-07-30 17:22:36,780 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:22:37,222 - main - INFO - Search for 'bu' returned 4 results
2025-07-30 17:22:37,380 - main - INFO - Search for 'but' returned 4 results
2025-07-30 17:22:38,511 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:22:38,638 - main - INFO - Search for 'it' returned 4 results
2025-07-30 17:22:39,019 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:22:39,188 - main - INFO - Search for 'st' returned 4 results
2025-07-30 17:22:39,385 - main - INFO - Search for 'sti' returned 4 results
2025-07-30 17:22:39,603 - main - INFO - Search for 'stil' returned 4 results
2025-07-30 17:22:39,786 - main - INFO - Search for 'still' returned 4 results
2025-07-30 17:22:40,247 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:22:40,426 - main - INFO - Search for 'co' returned 4 results
2025-07-30 17:22:40,516 - main - INFO - Search for 'cop' returned 4 results
2025-07-30 17:22:40,710 - main - INFO - Search for 'copi' returned 4 results
2025-07-30 17:22:40,828 - main - INFO - Search for 'copie' returned 4 results
2025-07-30 17:22:41,054 - main - INFO - Search for 'copies' returned 4 results
2025-07-30 17:22:41,385 - main - INFO - Search for 'o' returned 4 results
2025-07-30 17:22:41,533 - main - INFO - Search for 'on' returned 4 results
2025-07-30 17:22:42,004 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:22:42,122 - main - INFO - Search for 'do' returned 4 results
2025-07-30 17:22:42,451 - main - INFO - Search for 'doi' returned 4 results
2025-07-30 17:22:42,572 - main - INFO - Search for 'doiu' returned 4 results
2025-07-30 17:22:42,771 - main - INFO - Search for 'doiub' returned 4 results
2025-07-30 17:22:42,980 - main - INFO - Search for 'doiubl' returned 4 results
2025-07-30 17:22:43,125 - main - INFO - Search for 'doiuble' returned 4 results
2025-07-30 17:22:43,495 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:22:43,614 - main - INFO - Search for 'cl' returned 4 results
2025-07-30 17:22:43,840 - main - INFO - Search for 'cli' returned 4 results
2025-07-30 17:22:43,982 - main - INFO - Search for 'clic' returned 4 results
2025-07-30 17:22:44,539 - main - INFO - Search for 'click' returned 4 results
2025-07-30 17:22:45,885 - main - INFO - Search for 'clicka' returned 4 results
2025-07-30 17:22:46,083 - main - INFO - Search for 'clickan' returned 4 results
2025-07-30 17:22:46,177 - main - INFO - Search for 'clickand' returned 4 results
2025-07-30 17:22:46,462 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:22:46,633 - main - INFO - Search for 'do' returned 4 results
2025-07-30 17:22:46,774 - main - INFO - Search for 'doe' returned 4 results
2025-07-30 17:22:47,217 - main - INFO - Search for 'does' returned 4 results
2025-07-30 17:22:47,388 - main - INFO - Search for 'doesn' returned 4 results
2025-07-30 17:22:47,617 - main - INFO - Search for 'doesno' returned 4 results
2025-07-30 17:22:47,809 - main - INFO - Search for 'doesnot' returned 4 results
2025-07-30 17:22:48,150 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:22:48,249 - main - INFO - Search for 'al' returned 4 results
2025-07-30 17:22:48,417 - main - INFO - Search for 'all' returned 4 results
2025-07-30 17:22:48,623 - main - INFO - Search for 'allo' returned 4 results
2025-07-30 17:22:48,728 - main - INFO - Search for 'allow' returned 4 results
2025-07-30 17:22:50,076 - main - INFO - Search for 'm' returned 4 results
2025-07-30 17:22:50,179 - main - INFO - Search for 'me' returned 4 results
2025-07-30 17:22:50,661 - main - INFO - Search for 'o' returned 4 results
2025-07-30 17:22:51,389 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:22:51,786 - main - INFO - Search for 'to' returned 4 results
2025-07-30 17:22:52,845 - main - INFO - Search for 'm' returned 4 results
2025-07-30 17:22:53,011 - main - INFO - Search for 'mo' returned 4 results
2025-07-30 17:22:53,184 - main - INFO - Search for 'mov' returned 4 results
2025-07-30 17:22:53,277 - main - INFO - Search for 'move' returned 4 results
2025-07-30 17:22:53,787 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:22:53,885 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:22:54,001 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:22:54,404 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:22:54,504 - main - INFO - Search for 'ch' returned 4 results
2025-07-30 17:22:54,754 - main - INFO - Search for 'chi' returned 4 results
2025-07-30 17:22:55,088 - main - INFO - Search for 'chik' returned 4 results
2025-07-30 17:22:56,841 - main - INFO - Search for 'chi' returned 4 results
2025-07-30 17:22:57,111 - main - INFO - Search for 'ch' returned 4 results
2025-07-30 17:22:57,819 - main - INFO - Search for 'chu' returned 4 results
2025-07-30 17:22:58,032 - main - INFO - Search for 'chun' returned 4 results
2025-07-30 17:22:58,145 - main - INFO - Search for 'chunk' returned 4 results
2025-07-30 17:22:58,691 - main - INFO - Search for 'h' returned 4 results
2025-07-30 17:22:58,830 - main - INFO - Search for 'he' returned 4 results
2025-07-30 17:22:58,956 - main - INFO - Search for 'her' returned 4 results
2025-07-30 17:22:59,092 - main - INFO - Search for 'here' returned 4 results
2025-07-30 17:22:59,375 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:22:59,469 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:22:59,756 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:23:00,141 - main - INFO - Search for 'dt' returned 4 results
2025-07-30 17:23:00,361 - main - INFO - Search for 'dth' returned 4 results
2025-07-30 17:23:00,484 - main - INFO - Search for 'dthe' returned 4 results
2025-07-30 17:23:00,598 - main - INFO - Search for 'dther' returned 4 results
2025-07-30 17:23:00,707 - main - INFO - Search for 'dthere' returned 4 results
2025-07-30 17:23:01,048 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:23:01,201 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:23:01,291 - main - INFO - Search for 'and' returned 4 results
2025-07-30 17:23:01,561 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:23:01,932 - main - INFO - Search for 'al' returned 4 results
2025-07-30 17:23:02,115 - main - INFO - Search for 'all' returned 4 results
2025-07-30 17:23:02,294 - main - INFO - Search for 'allo' returned 4 results
2025-07-30 17:23:02,415 - main - INFO - Search for 'allow' returned 4 results
2025-07-30 17:23:02,872 - main - INFO - Search for 'm' returned 4 results
2025-07-30 17:23:03,001 - main - INFO - Search for 'me' returned 4 results
2025-07-30 17:23:03,295 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:23:03,464 - main - INFO - Search for 'to' returned 4 results
2025-07-30 17:23:03,882 - main - INFO - Search for 'p' returned 4 results
2025-07-30 17:23:04,008 - main - INFO - Search for 'pa' returned 4 results
2025-07-30 17:23:04,091 - main - INFO - Search for 'pas' returned 4 results
2025-07-30 17:23:04,338 - main - INFO - Search for 'past' returned 4 results
2025-07-30 17:23:04,490 - main - INFO - Search for 'paste' returned 4 results
2025-07-30 17:23:05,154 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:23:05,294 - main - INFO - Search for 'in' returned 4 results
2025-07-30 17:23:05,817 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:23:06,033 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:23:06,170 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:23:12,164 - main - INFO - Search for 'e' returned 4 results
2025-07-30 17:23:12,317 - main - INFO - Search for 'ed' returned 4 results
2025-07-30 17:23:12,579 - main - INFO - Search for 'edi' returned 4 results
2025-07-30 17:23:12,918 - main - INFO - Search for 'edit' returned 4 results
2025-07-30 17:23:14,437 - main - INFO - Search for 'editp' returned 4 results
2025-07-30 17:23:14,988 - main - INFO - Search for 'edit' returned 4 results
2025-07-30 17:23:15,332 - main - INFO - Search for 'edito' returned 4 results
2025-07-30 17:23:15,513 - main - INFO - Search for 'editor' returned 4 results
2025-07-30 17:23:19,251 - main - INFO - Search for 'editord' returned 4 results
2025-07-30 17:23:19,475 - main - INFO - Search for 'editordo' returned 4 results
2025-07-30 17:23:19,629 - main - INFO - Search for 'editordoe' returned 4 results
2025-07-30 17:23:19,866 - main - INFO - Search for 'editordoes' returned 4 results
2025-07-30 17:23:19,993 - main - INFO - Search for 'editordoesn' returned 4 results
2025-07-30 17:23:20,199 - main - INFO - Search for 'editordoesno' returned 4 results
2025-07-30 17:23:20,406 - main - INFO - Search for 'editordoesnot' returned 4 results
2025-07-30 17:23:24,213 - main - INFO - Search for 'p' returned 4 results
2025-07-30 17:23:24,381 - main - INFO - Search for 'pl' returned 4 results
2025-07-30 17:23:24,539 - main - INFO - Search for 'ple' returned 4 results
2025-07-30 17:23:24,735 - main - INFO - Search for 'plea' returned 4 results
2025-07-30 17:23:24,822 - main - INFO - Search for 'pleas' returned 4 results
2025-07-30 17:23:25,042 - main - INFO - Search for 'please' returned 4 results
2025-07-30 17:23:25,443 - main - INFO - Search for 'f' returned 4 results
2025-07-30 17:23:25,617 - main - INFO - Search for 'fi' returned 4 results
2025-07-30 17:23:25,810 - main - INFO - Search for 'fix' returned 4 results
2025-07-30 17:23:26,300 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:23:26,572 - main - INFO - Search for 'iw' returned 4 results
2025-07-30 17:23:27,096 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:23:27,287 - main - INFO - Search for 'wa' returned 4 results
2025-07-30 17:23:27,380 - main - INFO - Search for 'wan' returned 4 results
2025-07-30 17:23:27,652 - main - INFO - Search for 'want' returned 4 results
2025-07-30 17:23:28,550 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:23:28,708 - main - INFO - Search for 'wh' returned 4 results
2025-07-30 17:23:28,851 - main - INFO - Search for 'who' returned 4 results
2025-07-30 17:23:29,125 - main - INFO - Search for 'whol' returned 4 results
2025-07-30 17:23:29,275 - main - INFO - Search for 'whole' returned 4 results
2025-07-30 17:23:29,627 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:23:29,808 - main - INFO - Search for 'ch' returned 4 results
2025-07-30 17:23:30,071 - main - INFO - Search for 'cho' returned 4 results
2025-07-30 17:23:30,157 - main - INFO - Search for 'chok' returned 4 results
2025-07-30 17:23:30,340 - main - INFO - Search for 'choke' returned 4 results
2025-07-30 17:23:31,029 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:23:31,156 - main - INFO - Search for 'to' returned 4 results
2025-07-30 17:23:31,537 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:23:31,615 - main - INFO - Search for 'be' returned 4 results
2025-07-30 17:23:32,009 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:23:32,187 - main - INFO - Search for 'dr' returned 4 results
2025-07-30 17:23:32,405 - main - INFO - Search for 'dra' returned 4 results
2025-07-30 17:23:32,565 - main - INFO - Search for 'drag' returned 4 results
2025-07-30 17:23:32,825 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:23:32,979 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:23:33,070 - main - INFO - Search for 'and' returned 4 results
2025-07-30 17:23:33,349 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:23:33,564 - main - INFO - Search for 'dr' returned 4 results
2025-07-30 17:23:33,739 - main - INFO - Search for 'dro' returned 4 results
2025-07-30 17:23:33,981 - main - INFO - Search for 'drop' returned 4 results
2025-07-30 17:23:34,097 - main - INFO - Search for 'dropp' returned 4 results
2025-07-30 17:23:34,209 - main - INFO - Search for 'droppe' returned 4 results
2025-07-30 17:23:34,565 - main - INFO - Search for 'dropped' returned 4 results
2025-07-30 17:23:34,865 - main - INFO - Search for 'r' returned 4 results
2025-07-30 17:23:34,952 - main - INFO - Search for 're' returned 4 results
2025-07-30 17:23:35,198 - main - INFO - Search for 'rem' returned 4 results
2025-07-30 17:23:35,356 - main - INFO - Search for 'remo' returned 4 results
2025-07-30 17:23:35,491 - main - INFO - Search for 'remov' returned 4 results
2025-07-30 17:23:35,565 - main - INFO - Search for 'remove' returned 4 results
2025-07-30 17:23:36,590 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:23:36,883 - main - INFO - Search for 'do' returned 4 results
2025-07-30 17:23:37,129 - main - INFO - Search for 'dou' returned 4 results
2025-07-30 17:23:37,318 - main - INFO - Search for 'doub' returned 4 results
2025-07-30 17:23:37,525 - main - INFO - Search for 'doubl' returned 4 results
2025-07-30 17:23:38,060 - main - INFO - Search for 'double' returned 4 results
2025-07-30 17:23:38,449 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:23:38,670 - main - INFO - Search for 'cl' returned 4 results
2025-07-30 17:23:38,812 - main - INFO - Search for 'cli' returned 4 results
2025-07-30 17:23:38,972 - main - INFO - Search for 'clic' returned 4 results
2025-07-30 17:23:39,093 - main - INFO - Search for 'click' returned 4 results
2025-07-30 17:23:39,591 - main - INFO - Search for 'f' returned 4 results
2025-07-30 17:23:39,768 - main - INFO - Search for 'fu' returned 4 results
2025-07-30 17:23:39,921 - main - INFO - Search for 'fun' returned 4 results
2025-07-30 17:23:40,069 - main - INFO - Search for 'func' returned 4 results
2025-07-30 17:23:40,272 - main - INFO - Search for 'funct' returned 4 results
2025-07-30 17:23:40,464 - main - INFO - Search for 'functi' returned 4 results
2025-07-30 17:23:40,643 - main - INFO - Search for 'functio' returned 4 results
2025-07-30 17:23:40,821 - main - INFO - Search for 'functioa' returned 4 results
2025-07-30 17:23:40,969 - main - INFO - Search for 'functioan' returned 4 results
2025-07-30 17:23:40,985 - main - INFO - Search for 'functioanl' returned 4 results
2025-07-30 17:23:41,177 - main - INFO - Search for 'functioanli' returned 4 results
2025-07-30 17:23:41,519 - main - INFO - Search for 'functioanliy' returned 4 results
2025-07-30 17:23:41,534 - main - INFO - Search for 'functioanliyt' returned 4 results
2025-07-30 17:23:41,929 - main - INFO - Search for 'functioanliyty' returned 4 results
2025-07-30 17:28:50,380 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-30 17:29:03,047 - datasets - INFO - PyTorch version 2.4.0 available.
2025-07-30 17:29:03,054 - datasets - INFO - JAX version 0.5.2 available.
2025-07-30 17:29:03,822 - folder_manager - INFO - Loaded 2 connected folders
2025-07-30 17:29:03,833 - main - INFO - Initializing document search backend...
2025-07-30 17:29:03,833 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:29:03,836 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-30 17:29:03,837 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:29:10,272 - database - INFO - Connected to existing table: documents
2025-07-30 17:29:10,273 - database - INFO - Vector store initialized successfully
2025-07-30 17:29:10,275 - document_processor - INFO - Document processor initialized
2025-07-30 17:29:10,422 - main - INFO - Added test_docs folder to monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:29:10,422 - folder_manager - INFO - Starting folder monitoring...
2025-07-30 17:29:10,430 - folder_manager - INFO - Started monitoring folder: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:29:10,431 - folder_manager - WARNING - Folder does not exist, skipping monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-30 17:29:10,432 - folder_manager - INFO - Monitoring 2 folders
2025-07-30 17:29:10,437 - api_service - INFO - API server started successfully
2025-07-30 17:29:10,477 - folder_manager - INFO - Starting background document processor...
2025-07-30 17:29:25,299 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:29:25,539 - main - INFO - Search for 'bi' returned 4 results
2025-07-30 17:29:25,682 - main - INFO - Search for 'bil' returned 4 results
2025-07-30 17:29:58,587 - main - INFO - Search for 'bi' returned 4 results
2025-07-30 17:29:58,861 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:29:59,962 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:30:00,051 - main - INFO - Search for 'bi' returned 4 results
2025-07-30 17:30:00,258 - main - INFO - Search for 'bil' returned 4 results
2025-07-30 17:30:00,434 - main - INFO - Search for 'bila' returned 4 results
2025-07-30 17:30:00,573 - main - INFO - Search for 'bilal' returned 4 results
2025-07-30 17:30:30,665 - main - INFO - Search for 'bilalS' returned 4 results
2025-07-30 17:30:44,275 - main - INFO - Search for 'bilalSa' returned 4 results
2025-07-30 17:30:45,046 - main - INFO - Search for 'bilalSai' returned 4 results
2025-07-30 17:30:45,429 - main - INFO - Search for 'l' returned 4 results
2025-07-30 17:30:45,513 - main - INFO - Search for 'li' returned 4 results
2025-07-30 17:30:45,674 - main - INFO - Search for 'lik' returned 4 results
2025-07-30 17:30:45,793 - main - INFO - Search for 'like' returned 4 results
2025-07-30 17:30:46,152 - main - INFO - Search for 'h' returned 4 results
2025-07-30 17:30:46,229 - main - INFO - Search for 'ho' returned 4 results
2025-07-30 17:30:46,326 - main - INFO - Search for 'how' returned 4 results
2025-07-30 17:30:46,707 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:30:46,971 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:30:47,117 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:30:47,472 - main - INFO - Search for 'g' returned 4 results
2025-07-30 17:30:47,545 - main - INFO - Search for 'gr' returned 4 results
2025-07-30 17:30:47,783 - main - INFO - Search for 'gre' returned 4 results
2025-07-30 17:30:47,939 - main - INFO - Search for 'gree' returned 4 results
2025-07-30 17:30:48,075 - main - INFO - Search for 'green' returned 4 results
2025-07-30 17:30:48,953 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:30:49,070 - main - INFO - Search for 'bo' returned 4 results
2025-07-30 17:30:49,191 - main - INFO - Search for 'box' returned 4 results
2025-07-30 17:30:49,853 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:30:49,986 - main - INFO - Search for 'is' returned 4 results
2025-07-30 17:30:51,082 - main - INFO - Search for 'm' returned 4 results
2025-07-30 17:30:51,153 - main - INFO - Search for 'mo' returned 4 results
2025-07-30 17:30:51,248 - main - INFO - Search for 'mov' returned 4 results
2025-07-30 17:30:51,301 - main - INFO - Search for 'move' returned 4 results
2025-07-30 17:30:51,528 - main - INFO - Search for 'moves' returned 4 results
2025-07-30 17:30:51,832 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:30:51,939 - main - INFO - Search for 'wi' returned 4 results
2025-07-30 17:30:52,069 - main - INFO - Search for 'wit' returned 4 results
2025-07-30 17:30:52,186 - main - INFO - Search for 'with' returned 4 results
2025-07-30 17:30:52,438 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:30:52,548 - main - INFO - Search for 'cu' returned 4 results
2025-07-30 17:30:52,676 - main - INFO - Search for 'cur' returned 4 results
2025-07-30 17:30:52,907 - main - INFO - Search for 'curs' returned 4 results
2025-07-30 17:30:53,051 - main - INFO - Search for 'curso' returned 4 results
2025-07-30 17:30:53,113 - main - INFO - Search for 'cursor' returned 4 results
2025-07-30 17:30:53,631 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:30:53,668 - main - INFO - Search for 'bu' returned 4 results
2025-07-30 17:30:53,789 - main - INFO - Search for 'but' returned 4 results
2025-07-30 17:30:54,357 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:30:54,441 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:30:54,582 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:30:54,860 - main - INFO - Search for 'p' returned 4 results
2025-07-30 17:30:54,947 - main - INFO - Search for 'pr' returned 4 results
2025-07-30 17:30:55,070 - main - INFO - Search for 'pro' returned 4 results
2025-07-30 17:30:55,218 - main - INFO - Search for 'prob' returned 4 results
2025-07-30 17:30:55,372 - main - INFO - Search for 'probl' returned 4 results
2025-07-30 17:30:55,473 - main - INFO - Search for 'proble' returned 4 results
2025-07-30 17:30:55,619 - main - INFO - Search for 'problem' returned 4 results
2025-07-30 17:30:55,802 - main - INFO - Search for 'problems' returned 4 results
2025-07-30 17:30:56,402 - main - INFO - Search for 'problem' returned 4 results
2025-07-30 17:30:56,589 - main - INFO - Search for 'problemm' returned 4 results
2025-07-30 17:30:57,012 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:30:57,131 - main - INFO - Search for 'is' returned 4 results
2025-07-30 17:30:57,476 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:30:57,546 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:30:57,664 - main - INFO - Search for 'tha' returned 4 results
2025-07-30 17:30:57,764 - main - INFO - Search for 'that' returned 4 results
2025-07-30 17:30:58,090 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:30:58,217 - main - INFO - Search for 'it' returned 4 results
2025-07-30 17:30:58,494 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:30:58,739 - main - INFO - Search for 'st' returned 4 results
2025-07-30 17:30:58,938 - main - INFO - Search for 'sta' returned 4 results
2025-07-30 17:30:59,087 - main - INFO - Search for 'stay' returned 4 results
2025-07-30 17:30:59,204 - main - INFO - Search for 'stays' returned 4 results
2025-07-30 17:30:59,615 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:30:59,662 - main - INFO - Search for 'in' returned 4 results
2025-07-30 17:30:59,997 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:31:00,131 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:31:00,256 - main - INFO - Search for 'tha' returned 4 results
2025-07-30 17:31:00,364 - main - INFO - Search for 'that' returned 4 results
2025-07-30 17:31:00,689 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:31:00,881 - main - INFO - Search for 'ap' returned 4 results
2025-07-30 17:31:01,006 - main - INFO - Search for 'app' returned 4 results
2025-07-30 17:31:01,149 - main - INFO - Search for 'appl' returned 4 results
2025-07-30 17:31:01,421 - main - INFO - Search for 'appli' returned 4 results
2025-07-30 17:31:01,480 - main - INFO - Search for 'applic' returned 4 results
2025-07-30 17:31:01,546 - main - INFO - Search for 'applica' returned 4 results
2025-07-30 17:31:01,839 - main - INFO - Search for 'applicat' returned 4 results
2025-07-30 17:31:02,056 - main - INFO - Search for 'applicati' returned 4 results
2025-07-30 17:31:02,101 - main - INFO - Search for 'applicatio' returned 4 results
2025-07-30 17:31:02,381 - main - INFO - Search for 'application' returned 4 results
2025-07-30 17:31:03,526 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:31:03,739 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:31:03,896 - main - INFO - Search for 'and' returned 4 results
2025-07-30 17:31:04,206 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:31:04,377 - main - INFO - Search for 'wh' returned 4 results
2025-07-30 17:31:04,756 - main - INFO - Search for 'whn' returned 4 results
2025-07-30 17:31:05,221 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:31:05,641 - main - INFO - Search for 'm' returned 4 results
2025-07-30 17:31:05,718 - main - INFO - Search for 'mo' returned 4 results
2025-07-30 17:31:05,924 - main - INFO - Search for 'mov' returned 4 results
2025-07-30 17:31:05,992 - main - INFO - Search for 'move' returned 4 results
2025-07-30 17:31:06,335 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:31:06,439 - main - INFO - Search for 'it' returned 4 results
2025-07-30 17:31:06,853 - main - INFO - Search for 'u' returned 4 results
2025-07-30 17:31:07,039 - main - INFO - Search for 'up' returned 4 results
2025-07-30 17:31:07,672 - main - INFO - Search for 'o' returned 4 results
2025-07-30 17:31:07,774 - main - INFO - Search for 'or' returned 4 results
2025-07-30 17:31:08,060 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:31:08,230 - main - INFO - Search for 'dw' returned 4 results
2025-07-30 17:31:08,268 - main - INFO - Search for 'dwo' returned 4 results
2025-07-30 17:31:08,404 - main - INFO - Search for 'dwon' returned 4 results
2025-07-30 17:31:08,784 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:31:08,926 - main - INFO - Search for 'it' returned 4 results
2025-07-30 17:31:09,252 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:31:09,586 - main - INFO - Search for 'st' returned 4 results
2025-07-30 17:31:09,813 - main - INFO - Search for 'sta' returned 4 results
2025-07-30 17:31:10,040 - main - INFO - Search for 'star' returned 4 results
2025-07-30 17:31:10,235 - main - INFO - Search for 'start' returned 4 results
2025-07-30 17:31:11,184 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:31:11,684 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:31:11,758 - main - INFO - Search for 'co' returned 4 results
2025-07-30 17:31:11,907 - main - INFO - Search for 'cop' returned 4 results
2025-07-30 17:31:12,165 - main - INFO - Search for 'copy' returned 4 results
2025-07-30 17:31:12,402 - main - INFO - Search for 'copyi' returned 4 results
2025-07-30 17:31:12,504 - main - INFO - Search for 'copyin' returned 4 results
2025-07-30 17:31:12,589 - main - INFO - Search for 'copying' returned 4 results
2025-07-30 17:31:13,309 - main - INFO - Search for 'e' returned 4 results
2025-07-30 17:31:14,320 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:31:14,469 - main - INFO - Search for 'te' returned 4 results
2025-07-30 17:31:14,707 - main - INFO - Search for 'tex' returned 4 results
2025-07-30 17:31:14,850 - main - INFO - Search for 'text' returned 4 results
2025-07-30 17:31:15,754 - main - INFO - Search for 'f' returned 4 results
2025-07-30 17:31:15,985 - main - INFO - Search for 'fr' returned 4 results
2025-07-30 17:31:16,117 - main - INFO - Search for 'fro' returned 4 results
2025-07-30 17:31:16,219 - main - INFO - Search for 'from' returned 4 results
2025-07-30 17:31:31,236 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:31:31,369 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:31:31,520 - main - INFO - Search for 'tha' returned 4 results
2025-07-30 17:31:31,631 - main - INFO - Search for 'that' returned 4 results
2025-07-30 17:31:32,300 - main - INFO - Search for 'thatv' returned 4 results
2025-07-30 17:31:33,737 - main - INFO - Search for 'thatvl' returned 4 results
2025-07-30 17:31:33,893 - main - INFO - Search for 'thatvli' returned 4 results
2025-07-30 17:31:34,668 - main - INFO - Search for 'thatvl' returned 4 results
2025-07-30 17:31:34,818 - main - INFO - Search for 'thatv' returned 4 results
2025-07-30 17:31:37,907 - main - INFO - Search for 'that' returned 4 results
2025-07-30 17:31:38,076 - main - INFO - Search for 'tha' returned 4 results
2025-07-30 17:31:38,331 - main - INFO - Search for 'thae' returned 4 results
2025-07-30 17:31:38,641 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:31:38,775 - main - INFO - Search for 'sa' returned 4 results
2025-07-30 17:31:38,897 - main - INFO - Search for 'sam' returned 4 results
2025-07-30 17:31:39,012 - main - INFO - Search for 'same' returned 4 results
2025-07-30 17:31:39,747 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:31:39,995 - main - INFO - Search for 'ap' returned 4 results
2025-07-30 17:31:40,127 - main - INFO - Search for 'app' returned 4 results
2025-07-30 17:31:40,415 - main - INFO - Search for 'appl' returned 4 results
2025-07-30 17:31:40,513 - main - INFO - Search for 'appli' returned 4 results
2025-07-30 17:31:40,604 - main - INFO - Search for 'applic' returned 4 results
2025-07-30 17:31:40,737 - main - INFO - Search for 'applica' returned 4 results
2025-07-30 17:31:40,924 - main - INFO - Search for 'applicat' returned 4 results
2025-07-30 17:31:40,980 - main - INFO - Search for 'applicati' returned 4 results
2025-07-30 17:31:41,115 - main - INFO - Search for 'applicatio' returned 4 results
2025-07-30 17:31:41,191 - main - INFO - Search for 'application' returned 4 results
2025-07-30 17:31:41,506 - main - INFO - Search for 'l' returned 4 results
2025-07-30 17:31:41,626 - main - INFO - Search for 'li' returned 4 results
2025-07-30 17:31:41,775 - main - INFO - Search for 'lik' returned 4 results
2025-07-30 17:31:41,854 - main - INFO - Search for 'like' returned 4 results
2025-07-30 17:31:42,088 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:31:42,219 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:31:42,288 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:31:42,444 - main - INFO - Search for 'show' returned 4 results
2025-07-30 17:31:43,068 - main - INFO - Search for 'shown' returned 4 results
2025-07-30 17:31:43,633 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:31:43,731 - main - INFO - Search for 'be' returned 4 results
2025-07-30 17:31:43,838 - main - INFO - Search for 'bel' returned 4 results
2025-07-30 17:31:44,027 - main - INFO - Search for 'belo' returned 4 results
2025-07-30 17:31:44,164 - main - INFO - Search for 'below' returned 4 results
2025-07-30 17:31:44,552 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:31:44,642 - main - INFO - Search for 'in' returned 4 results
2025-07-30 17:31:45,026 - main - INFO - Search for 'p' returned 4 results
2025-07-30 17:31:45,216 - main - INFO - Search for 'pi' returned 4 results
2025-07-30 17:31:45,375 - main - INFO - Search for 'pic' returned 4 results
2025-07-30 17:31:45,654 - main - INFO - Search for 'pict' returned 4 results
2025-07-30 17:31:45,825 - main - INFO - Search for 'pictu' returned 4 results
2025-07-30 17:31:45,904 - main - INFO - Search for 'pictur' returned 4 results
2025-07-30 17:31:46,055 - main - INFO - Search for 'picture' returned 4 results
2025-07-30 17:31:47,772 - main - INFO - Search for 'picturet' returned 4 results
2025-07-30 17:31:48,145 - main - INFO - Search for 'pictureth' returned 4 results
2025-07-30 17:31:48,218 - main - INFO - Search for 'picturethi' returned 4 results
2025-07-30 17:31:48,301 - main - INFO - Search for 'picturethis' returned 4 results
2025-07-30 17:31:49,343 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:31:49,458 - main - INFO - Search for 'is' returned 4 results
2025-07-30 17:31:49,749 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:31:49,826 - main - INFO - Search for 'ba' returned 4 results
2025-07-30 17:31:49,881 - main - INFO - Search for 'bas' returned 4 results
2025-07-30 17:31:50,082 - main - INFO - Search for 'basi' returned 4 results
2025-07-30 17:31:50,124 - main - INFO - Search for 'basic' returned 4 results
2025-07-30 17:31:50,237 - main - INFO - Search for 'basica' returned 4 results
2025-07-30 17:31:50,334 - main - INFO - Search for 'basical' returned 4 results
2025-07-30 17:31:50,495 - main - INFO - Search for 'basicall' returned 4 results
2025-07-30 17:31:50,734 - main - INFO - Search for 'basically' returned 4 results
2025-07-30 17:31:51,083 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:31:51,696 - main - INFO - Search for 'f' returned 4 results
2025-07-30 17:31:51,793 - main - INFO - Search for 'fl' returned 4 results
2025-07-30 17:31:51,941 - main - INFO - Search for 'flo' returned 4 results
2025-07-30 17:31:52,020 - main - INFO - Search for 'floa' returned 4 results
2025-07-30 17:31:52,226 - main - INFO - Search for 'float' returned 4 results
2025-07-30 17:31:52,338 - main - INFO - Search for 'floati' returned 4 results
2025-07-30 17:31:52,407 - main - INFO - Search for 'floatin' returned 4 results
2025-07-30 17:31:52,520 - main - INFO - Search for 'floating' returned 4 results
2025-07-30 17:31:52,803 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:31:53,102 - main - INFO - Search for 'wi' returned 4 results
2025-07-30 17:31:53,256 - main - INFO - Search for 'win' returned 4 results
2025-07-30 17:31:53,402 - main - INFO - Search for 'wind' returned 4 results
2025-07-30 17:31:53,518 - main - INFO - Search for 'windo' returned 4 results
2025-07-30 17:31:53,678 - main - INFO - Search for 'window' returned 4 results
2025-07-30 17:31:54,987 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:31:55,141 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:31:55,280 - main - INFO - Search for 'and' returned 4 results
2025-07-30 17:31:59,022 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:31:59,152 - main - INFO - Search for 'wh' returned 4 results
2025-07-30 17:31:59,324 - main - INFO - Search for 'whe' returned 4 results
2025-07-30 17:31:59,418 - main - INFO - Search for 'when' returned 4 results
2025-07-30 17:31:59,686 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:01,158 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:01,369 - main - INFO - Search for 'am' returned 4 results
2025-07-30 17:32:01,801 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:32:01,974 - main - INFO - Search for 'wr' returned 4 results
2025-07-30 17:32:02,103 - main - INFO - Search for 'wri' returned 4 results
2025-07-30 17:32:02,219 - main - INFO - Search for 'writ' returned 4 results
2025-07-30 17:32:02,331 - main - INFO - Search for 'writi' returned 4 results
2025-07-30 17:32:02,401 - main - INFO - Search for 'writin' returned 4 results
2025-07-30 17:32:02,542 - main - INFO - Search for 'writing' returned 4 results
2025-07-30 17:32:02,778 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:02,824 - main - INFO - Search for 'in' returned 4 results
2025-07-30 17:32:03,167 - main - INFO - Search for 'm' returned 4 results
2025-07-30 17:32:03,352 - main - INFO - Search for 'my' returned 4 results
2025-07-30 17:32:03,688 - main - INFO - Search for 'e' returned 4 results
2025-07-30 17:32:03,783 - main - INFO - Search for 'ed' returned 4 results
2025-07-30 17:32:03,906 - main - INFO - Search for 'edi' returned 4 results
2025-07-30 17:32:04,047 - main - INFO - Search for 'edit' returned 4 results
2025-07-30 17:32:04,222 - main - INFO - Search for 'edito' returned 4 results
2025-07-30 17:32:04,351 - main - INFO - Search for 'editor' returned 4 results
2025-07-30 17:32:05,207 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:07,546 - main - INFO - Search for 'm' returned 4 results
2025-07-30 17:32:07,796 - main - INFO - Search for 'my' returned 4 results
2025-07-30 17:32:08,117 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:32:08,247 - main - INFO - Search for 'cu' returned 4 results
2025-07-30 17:32:08,307 - main - INFO - Search for 'cur' returned 4 results
2025-07-30 17:32:08,607 - main - INFO - Search for 'curo' returned 4 results
2025-07-30 17:32:08,787 - main - INFO - Search for 'curor' returned 4 results
2025-07-30 17:32:09,159 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:32:09,326 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:32:09,368 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:32:09,486 - main - INFO - Search for 'shou' returned 4 results
2025-07-30 17:32:09,653 - main - INFO - Search for 'shoul' returned 4 results
2025-07-30 17:32:09,687 - main - INFO - Search for 'should' returned 4 results
2025-07-30 17:32:10,101 - main - INFO - Search for 'r' returned 4 results
2025-07-30 17:32:10,212 - main - INFO - Search for 're' returned 4 results
2025-07-30 17:32:10,301 - main - INFO - Search for 'rem' returned 4 results
2025-07-30 17:32:10,509 - main - INFO - Search for 'rema' returned 4 results
2025-07-30 17:32:10,601 - main - INFO - Search for 'remai' returned 4 results
2025-07-30 17:32:10,683 - main - INFO - Search for 'remain' returned 4 results
2025-07-30 17:32:11,648 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:11,718 - main - INFO - Search for 'in' returned 4 results
2025-07-30 17:32:12,070 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:32:12,183 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:32:12,291 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:32:12,564 - main - INFO - Search for 'e' returned 4 results
2025-07-30 17:32:12,896 - main - INFO - Search for 'ed' returned 4 results
2025-07-30 17:32:13,107 - main - INFO - Search for 'edi' returned 4 results
2025-07-30 17:32:13,221 - main - INFO - Search for 'edit' returned 4 results
2025-07-30 17:32:13,378 - main - INFO - Search for 'edito' returned 4 results
2025-07-30 17:32:13,474 - main - INFO - Search for 'editor' returned 4 results
2025-07-30 17:32:13,801 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:13,941 - main - INFO - Search for 'ap' returned 4 results
2025-07-30 17:32:14,112 - main - INFO - Search for 'app' returned 4 results
2025-07-30 17:32:14,316 - main - INFO - Search for 'appl' returned 4 results
2025-07-30 17:32:14,370 - main - INFO - Search for 'appli' returned 4 results
2025-07-30 17:32:14,481 - main - INFO - Search for 'applic' returned 4 results
2025-07-30 17:32:14,548 - main - INFO - Search for 'applica' returned 4 results
2025-07-30 17:32:14,812 - main - INFO - Search for 'applicat' returned 4 results
2025-07-30 17:32:14,875 - main - INFO - Search for 'applicati' returned 4 results
2025-07-30 17:32:15,004 - main - INFO - Search for 'applicatio' returned 4 results
2025-07-30 17:32:15,058 - main - INFO - Search for 'application' returned 4 results
2025-07-30 17:32:15,699 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:32:15,808 - main - INFO - Search for 'be' returned 4 results
2025-07-30 17:32:16,008 - main - INFO - Search for 'bec' returned 4 results
2025-07-30 17:32:16,179 - main - INFO - Search for 'beca' returned 4 results
2025-07-30 17:32:16,406 - main - INFO - Search for 'becas' returned 4 results
2025-07-30 17:32:16,445 - main - INFO - Search for 'becasu' returned 4 results
2025-07-30 17:32:16,664 - main - INFO - Search for 'becasue' returned 4 results
2025-07-30 17:32:17,690 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:32:17,806 - main - INFO - Search for 'cu' returned 4 results
2025-07-30 17:32:17,957 - main - INFO - Search for 'cur' returned 4 results
2025-07-30 17:32:18,115 - main - INFO - Search for 'curr' returned 4 results
2025-07-30 17:32:18,210 - main - INFO - Search for 'curre' returned 4 results
2025-07-30 17:32:18,320 - main - INFO - Search for 'curren' returned 4 results
2025-07-30 17:32:18,416 - main - INFO - Search for 'current' returned 4 results
2025-07-30 17:32:18,502 - main - INFO - Search for 'currentl' returned 4 results
2025-07-30 17:32:18,728 - main - INFO - Search for 'currently' returned 4 results
2025-07-30 17:32:19,103 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:19,364 - main - INFO - Search for 'ih' returned 4 results
2025-07-30 17:32:19,638 - main - INFO - Search for 'iha' returned 4 results
2025-07-30 17:32:19,731 - main - INFO - Search for 'ihav' returned 4 results
2025-07-30 17:32:19,923 - main - INFO - Search for 'ihave' returned 4 results
2025-07-30 17:32:20,232 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:32:20,301 - main - INFO - Search for 'to' returned 4 results
2025-07-30 17:32:20,539 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:32:20,640 - main - INFO - Search for 'cl' returned 4 results
2025-07-30 17:32:20,736 - main - INFO - Search for 'cli' returned 4 results
2025-07-30 17:32:20,841 - main - INFO - Search for 'clic' returned 4 results
2025-07-30 17:32:20,970 - main - INFO - Search for 'click' returned 4 results
2025-07-30 17:32:21,523 - main - INFO - Search for 'm' returned 4 results
2025-07-30 17:32:22,317 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:32:22,402 - main - INFO - Search for 'ba' returned 4 results
2025-07-30 17:32:22,547 - main - INFO - Search for 'bac' returned 4 results
2025-07-30 17:32:22,646 - main - INFO - Search for 'back' returned 4 results
2025-07-30 17:32:23,734 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:23,781 - main - INFO - Search for 'in' returned 4 results
2025-07-30 17:32:24,242 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:24,350 - main - INFO - Search for 'ap' returned 4 results
2025-07-30 17:32:24,905 - main - INFO - Search for 'app' returned 4 results
2025-07-30 17:32:25,130 - main - INFO - Search for 'appl' returned 4 results
2025-07-30 17:32:25,186 - main - INFO - Search for 'appli' returned 4 results
2025-07-30 17:32:25,272 - main - INFO - Search for 'applic' returned 4 results
2025-07-30 17:32:25,352 - main - INFO - Search for 'applica' returned 4 results
2025-07-30 17:32:25,552 - main - INFO - Search for 'applicat' returned 4 results
2025-07-30 17:32:25,618 - main - INFO - Search for 'applicati' returned 4 results
2025-07-30 17:32:25,723 - main - INFO - Search for 'applicatio' returned 4 results
2025-07-30 17:32:25,798 - main - INFO - Search for 'application' returned 4 results
2025-07-30 17:32:26,177 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:32:26,367 - main - INFO - Search for 'se' returned 4 results
2025-07-30 17:32:26,528 - main - INFO - Search for 'see' returned 4 results
2025-07-30 17:32:26,793 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:32:26,910 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:32:26,961 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:32:27,211 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:32:27,334 - main - INFO - Search for 'cu' returned 4 results
2025-07-30 17:32:27,446 - main - INFO - Search for 'cur' returned 4 results
2025-07-30 17:32:27,643 - main - INFO - Search for 'curs' returned 4 results
2025-07-30 17:32:27,789 - main - INFO - Search for 'curso' returned 4 results
2025-07-30 17:32:27,906 - main - INFO - Search for 'cursor' returned 4 results
2025-07-30 17:32:32,146 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:32:32,255 - main - INFO - Search for 'wh' returned 4 results
2025-07-30 17:32:32,414 - main - INFO - Search for 'whe' returned 4 results
2025-07-30 17:32:32,499 - main - INFO - Search for 'when' returned 4 results
2025-07-30 17:32:32,975 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:33,648 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:34,117 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:32:34,345 - main - INFO - Search for 'dr' returned 4 results
2025-07-30 17:32:34,486 - main - INFO - Search for 'dra' returned 4 results
2025-07-30 17:32:34,683 - main - INFO - Search for 'drag' returned 4 results
2025-07-30 17:32:34,823 - main - INFO - Search for 'dragg' returned 4 results
2025-07-30 17:32:34,976 - main - INFO - Search for 'draggi' returned 4 results
2025-07-30 17:32:35,055 - main - INFO - Search for 'draggin' returned 4 results
2025-07-30 17:32:35,192 - main - INFO - Search for 'dragging' returned 4 results
2025-07-30 17:32:35,557 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:35,667 - main - INFO - Search for 'it' returned 4 results
2025-07-30 17:32:36,316 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:32:36,370 - main - INFO - Search for 'ti' returned 4 results
2025-07-30 17:32:36,761 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:32:37,094 - main - INFO - Search for 'ti' returned 4 results
2025-07-30 17:32:37,187 - main - INFO - Search for 'tit' returned 4 results
2025-07-30 17:32:37,474 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:32:37,550 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:32:37,626 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:32:37,761 - main - INFO - Search for 'shou' returned 4 results
2025-07-30 17:32:37,846 - main - INFO - Search for 'shoud' returned 4 results
2025-07-30 17:32:37,927 - main - INFO - Search for 'shoudl' returned 4 results
2025-07-30 17:32:38,279 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:38,420 - main - INFO - Search for 'au' returned 4 results
2025-07-30 17:32:38,561 - main - INFO - Search for 'aut' returned 4 results
2025-07-30 17:32:38,731 - main - INFO - Search for 'auto' returned 4 results
2025-07-30 17:32:38,918 - main - INFO - Search for 'autoa' returned 4 results
2025-07-30 17:32:39,073 - main - INFO - Search for 'autoat' returned 4 results
2025-07-30 17:32:39,189 - main - INFO - Search for 'autoati' returned 4 results
2025-07-30 17:32:39,307 - main - INFO - Search for 'autoatic' returned 4 results
2025-07-30 17:32:39,435 - main - INFO - Search for 'autoatica' returned 4 results
2025-07-30 17:32:39,624 - main - INFO - Search for 'autoatical' returned 4 results
2025-07-30 17:32:39,740 - main - INFO - Search for 'autoaticall' returned 4 results
2025-07-30 17:32:39,988 - main - INFO - Search for 'autoatically' returned 4 results
2025-07-30 17:32:40,265 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:40,415 - main - INFO - Search for 'ac' returned 4 results
2025-07-30 17:32:40,629 - main - INFO - Search for 'act' returned 4 results
2025-07-30 17:32:40,708 - main - INFO - Search for 'acti' returned 4 results
2025-07-30 17:32:40,849 - main - INFO - Search for 'activ' returned 4 results
2025-07-30 17:32:40,956 - main - INFO - Search for 'activi' returned 4 results
2025-07-30 17:32:41,316 - main - INFO - Search for 'activia' returned 4 results
2025-07-30 17:32:41,608 - main - INFO - Search for 'activiat' returned 4 results
2025-07-30 17:32:41,697 - main - INFO - Search for 'activiate' returned 4 results
2025-07-30 17:32:43,796 - main - INFO - Search for 'm' returned 4 results
2025-07-30 17:32:44,064 - main - INFO - Search for 'my' returned 4 results
2025-07-30 17:32:44,372 - main - INFO - Search for 'e' returned 4 results
2025-07-30 17:32:44,470 - main - INFO - Search for 'ed' returned 4 results
2025-07-30 17:32:44,771 - main - INFO - Search for 'edt' returned 4 results
2025-07-30 17:32:45,195 - main - INFO - Search for 'edto' returned 4 results
2025-07-30 17:32:45,312 - main - INFO - Search for 'edtor' returned 4 results
2025-07-30 17:32:45,680 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:45,827 - main - INFO - Search for 'ap' returned 4 results
2025-07-30 17:32:45,982 - main - INFO - Search for 'app' returned 4 results
2025-07-30 17:32:46,214 - main - INFO - Search for 'appl' returned 4 results
2025-07-30 17:32:46,284 - main - INFO - Search for 'appli' returned 4 results
2025-07-30 17:32:46,400 - main - INFO - Search for 'applic' returned 4 results
2025-07-30 17:32:46,478 - main - INFO - Search for 'applica' returned 4 results
2025-07-30 17:32:46,684 - main - INFO - Search for 'applicat' returned 4 results
2025-07-30 17:32:46,836 - main - INFO - Search for 'applicati' returned 4 results
2025-07-30 17:32:47,001 - main - INFO - Search for 'applicatio' returned 4 results
2025-07-30 17:32:47,097 - main - INFO - Search for 'application' returned 4 results
2025-07-30 17:32:48,188 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:32:48,285 - main - INFO - Search for 'so' returned 4 results
2025-07-30 17:32:48,632 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:32:48,722 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:32:48,815 - main - INFO - Search for 'tha' returned 4 results
2025-07-30 17:32:48,880 - main - INFO - Search for 'that' returned 4 results
2025-07-30 17:32:49,165 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:32:49,276 - main - INFO - Search for 'cu' returned 4 results
2025-07-30 17:32:49,396 - main - INFO - Search for 'cur' returned 4 results
2025-07-30 17:32:49,625 - main - INFO - Search for 'curs' returned 4 results
2025-07-30 17:32:49,765 - main - INFO - Search for 'curso' returned 4 results
2025-07-30 17:32:49,881 - main - INFO - Search for 'cursor' returned 4 results
2025-07-30 17:32:50,127 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:32:50,256 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:32:50,308 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:32:50,406 - main - INFO - Search for 'show' returned 4 results
2025-07-30 17:32:50,613 - main - INFO - Search for 'shows' returned 4 results
2025-07-30 17:32:51,283 - main - INFO - Search for 'u' returned 4 results
2025-07-30 17:32:51,487 - main - INFO - Search for 'up' returned 4 results
2025-07-30 17:32:52,260 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:32:52,357 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:32:52,481 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:32:52,606 - main - INFO - Search for 'ther' returned 4 results
2025-07-30 17:32:52,751 - main - INFO - Search for 'there' returned 4 results
2025-07-30 17:32:52,968 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:53,128 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:32:53,168 - main - INFO - Search for 'and' returned 4 results
2025-07-30 17:32:53,510 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:53,811 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:53,889 - main - INFO - Search for 'am' returned 4 results
2025-07-30 17:32:54,267 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:32:54,586 - main - INFO - Search for 'al' returned 4 results
2025-07-30 17:32:54,766 - main - INFO - Search for 'all' returned 4 results
2025-07-30 17:32:54,959 - main - INFO - Search for 'allo' returned 4 results
2025-07-30 17:32:55,061 - main - INFO - Search for 'allow' returned 4 results
2025-07-30 17:32:55,186 - main - INFO - Search for 'allowe' returned 4 results
2025-07-30 17:32:55,450 - main - INFO - Search for 'allowed' returned 4 results
2025-07-30 17:32:55,705 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:32:55,815 - main - INFO - Search for 'to' returned 4 results
2025-07-30 17:32:56,084 - main - INFO - Search for 'p' returned 4 results
2025-07-30 17:32:56,170 - main - INFO - Search for 'pa' returned 4 results
2025-07-30 17:32:56,236 - main - INFO - Search for 'pas' returned 4 results
2025-07-30 17:32:56,453 - main - INFO - Search for 'past' returned 4 results
2025-07-30 17:32:56,572 - main - INFO - Search for 'paste' returned 4 results
2025-07-30 17:32:56,964 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:32:57,079 - main - INFO - Search for 'it' returned 4 results
2025-07-30 17:32:57,327 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:32:57,453 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:32:57,535 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:32:57,652 - main - INFO - Search for 'ther' returned 4 results
2025-07-30 17:32:57,778 - main - INFO - Search for 'there' returned 4 results
2025-07-30 17:36:07,551 - main - INFO - Search for 'therec' returned 4 results
2025-07-30 17:36:08,059 - main - INFO - Search for 'therecc' returned 4 results
2025-07-30 17:36:08,090 - main - INFO - Search for 'thereccc' returned 4 results
2025-07-30 17:36:09,418 - main - INFO - Search for 'therecccc' returned 4 results
2025-07-30 17:36:09,832 - main - INFO - Search for 'therecccco' returned 4 results
2025-07-30 17:36:10,011 - main - INFO - Search for 'thereccccon' returned 4 results
2025-07-30 17:36:10,242 - main - INFO - Search for 'thereccccont' returned 4 results
2025-07-30 17:36:10,352 - main - INFO - Search for 'therecccconti' returned 4 results
2025-07-30 17:36:10,478 - main - INFO - Search for 'thereccccontin' returned 4 results
2025-07-30 17:36:10,638 - main - INFO - Search for 'thereccccontinu' returned 4 results
2025-07-30 17:36:10,725 - main - INFO - Search for 'thereccccontinue' returned 4 results
2025-07-30 17:36:11,017 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:36:11,134 - main - INFO - Search for 'im' returned 4 results
2025-07-30 17:36:11,291 - main - INFO - Search for 'imp' returned 4 results
2025-07-30 17:36:11,354 - main - INFO - Search for 'impl' returned 4 results
2025-07-30 17:36:11,479 - main - INFO - Search for 'imple' returned 4 results
2025-07-30 17:36:11,604 - main - INFO - Search for 'implem' returned 4 results
2025-07-30 17:36:11,670 - main - INFO - Search for 'impleme' returned 4 results
2025-07-30 17:36:11,762 - main - INFO - Search for 'implemen' returned 4 results
2025-07-30 17:36:11,884 - main - INFO - Search for 'implement' returned 4 results
2025-07-30 17:36:12,055 - main - INFO - Search for 'implementa' returned 4 results
2025-07-30 17:36:12,130 - main - INFO - Search for 'implementat' returned 4 results
2025-07-30 17:36:12,209 - main - INFO - Search for 'implementati' returned 4 results
2025-07-30 17:36:12,304 - main - INFO - Search for 'implementatio' returned 4 results
2025-07-30 17:36:12,397 - main - INFO - Search for 'implementation' returned 4 results
2025-07-30 17:36:13,936 - main - INFO - Search for 'implementationv' returned 4 results
2025-07-30 17:39:19,048 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-30 17:39:33,007 - datasets - INFO - PyTorch version 2.4.0 available.
2025-07-30 17:39:33,016 - datasets - INFO - JAX version 0.5.2 available.
2025-07-30 17:39:33,870 - folder_manager - INFO - Loaded 2 connected folders
2025-07-30 17:39:33,883 - main - INFO - Initializing document search backend...
2025-07-30 17:39:33,883 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:39:33,888 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-30 17:39:33,888 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:39:39,167 - database - INFO - Connected to existing table: documents
2025-07-30 17:39:39,169 - database - INFO - Vector store initialized successfully
2025-07-30 17:39:39,169 - document_processor - INFO - Document processor initialized
2025-07-30 17:39:39,316 - main - INFO - Added test_docs folder to monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:39:39,316 - folder_manager - INFO - Starting folder monitoring...
2025-07-30 17:39:39,324 - folder_manager - INFO - Started monitoring folder: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:39:39,324 - folder_manager - WARNING - Folder does not exist, skipping monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-30 17:39:39,324 - folder_manager - INFO - Monitoring 2 folders
2025-07-30 17:39:39,333 - api_service - INFO - API server started successfully
2025-07-30 17:39:39,366 - folder_manager - INFO - Starting background document processor...
2025-07-30 17:40:56,008 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:40:57,420 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:40:57,549 - main - INFO - Search for 'bi' returned 4 results
2025-07-30 17:40:57,743 - main - INFO - Search for 'bil' returned 4 results
2025-07-30 17:40:57,988 - main - INFO - Search for 'bila' returned 4 results
2025-07-30 17:40:58,166 - main - INFO - Search for 'bilal' returned 4 results
2025-07-30 17:41:14,183 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:41:14,297 - main - INFO - Search for 'ij' returned 4 results
2025-07-30 17:41:14,428 - main - INFO - Search for 'ijb' returned 4 results
2025-07-30 17:41:15,856 - main - INFO - Search for 'ij' returned 4 results
2025-07-30 17:41:16,123 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:41:40,646 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-30 17:41:55,249 - datasets - INFO - PyTorch version 2.4.0 available.
2025-07-30 17:41:55,258 - datasets - INFO - JAX version 0.5.2 available.
2025-07-30 17:41:56,221 - folder_manager - INFO - Loaded 2 connected folders
2025-07-30 17:41:56,235 - main - INFO - Initializing document search backend...
2025-07-30 17:41:56,235 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:41:56,240 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-30 17:41:56,241 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:42:02,302 - database - INFO - Connected to existing table: documents
2025-07-30 17:42:02,302 - database - INFO - Vector store initialized successfully
2025-07-30 17:42:02,302 - document_processor - INFO - Document processor initialized
2025-07-30 17:42:02,426 - main - INFO - Added test_docs folder to monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:42:02,426 - folder_manager - INFO - Starting folder monitoring...
2025-07-30 17:42:02,426 - folder_manager - WARNING - Folder does not exist, skipping monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-30 17:42:02,435 - folder_manager - INFO - Started monitoring folder: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:42:02,435 - folder_manager - INFO - Monitoring 2 folders
2025-07-30 17:42:02,435 - api_service - INFO - API server started successfully
2025-07-30 17:42:02,459 - folder_manager - INFO - Starting background document processor...
2025-07-30 17:42:02,459 - database - INFO - Vector store closed
2025-07-30 17:42:02,459 - api_service - INFO - API server shutdown complete
2025-07-30 17:42:02,517 - folder_manager - INFO - Background processor stopped
2025-07-30 17:49:05,788 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-30 17:49:16,948 - datasets - INFO - PyTorch version 2.4.0 available.
2025-07-30 17:49:16,954 - datasets - INFO - JAX version 0.5.2 available.
2025-07-30 17:49:17,637 - folder_manager - INFO - Loaded 2 connected folders
2025-07-30 17:49:17,647 - main - INFO - Initializing document search backend...
2025-07-30 17:49:17,647 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:49:17,650 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-30 17:49:17,651 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-30 17:49:22,837 - database - INFO - Connected to existing table: documents
2025-07-30 17:49:22,837 - database - INFO - Vector store initialized successfully
2025-07-30 17:49:22,839 - document_processor - INFO - Document processor initialized
2025-07-30 17:49:22,971 - main - INFO - Added test_docs folder to monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:49:22,971 - folder_manager - INFO - Starting folder monitoring...
2025-07-30 17:49:22,974 - folder_manager - INFO - Started monitoring folder: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-30 17:49:22,975 - folder_manager - WARNING - Folder does not exist, skipping monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-30 17:49:22,975 - folder_manager - INFO - Monitoring 2 folders
2025-07-30 17:49:22,980 - api_service - INFO - API server started successfully
2025-07-30 17:49:23,018 - folder_manager - INFO - Starting background document processor...
2025-07-30 17:49:43,098 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:49:44,142 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:49:44,258 - main - INFO - Search for 'bi' returned 4 results
2025-07-30 17:49:44,508 - main - INFO - Search for 'bil' returned 4 results
2025-07-30 17:49:44,689 - main - INFO - Search for 'bila' returned 4 results
2025-07-30 17:49:44,839 - main - INFO - Search for 'bilal' returned 4 results
2025-07-30 17:49:48,557 - main - INFO - Search for 'bilalv' returned 4 results
2025-07-30 17:49:57,378 - main - INFO - Search for 'bilalvv' returned 4 results
2025-07-30 17:53:20,049 - main - INFO - Search for 'bilalvvv' returned 4 results
2025-07-30 17:54:46,529 - main - INFO - Search for 'bilalvvvv' returned 4 results
2025-07-30 17:54:55,736 - main - INFO - Search for 'bilalvvvvv' returned 4 results
2025-07-30 17:55:02,054 - main - INFO - Search for 'bilalvvvvvn' returned 4 results
2025-07-30 17:55:02,161 - main - INFO - Search for 'bilalvvvvvno' returned 4 results
2025-07-30 17:55:02,323 - main - INFO - Search for 'bilalvvvvvnot' returned 4 results
2025-07-30 17:55:11,653 - main - INFO - Search for 'bilalvvvvvnota' returned 4 results
2025-07-30 17:55:15,250 - main - INFO - Search for 'bilalvvvvvnotav' returned 4 results
2025-07-30 17:55:20,927 - main - INFO - Search for 'bilalvvvvvnotavv' returned 4 results
2025-07-30 17:55:26,911 - main - INFO - Search for 'bilalvvvvvnotavva' returned 4 results
2025-07-30 17:55:30,489 - main - INFO - Search for 'bilalvvvvvnotavvav' returned 4 results
2025-07-30 17:56:51,872 - main - INFO - Search for 'bilalvvvvvnotavvavc' returned 4 results
2025-07-30 17:56:52,466 - main - INFO - Search for 'bilalvvvvvnotavvavcc' returned 4 results
2025-07-30 17:57:08,935 - main - INFO - Search for 'bilalvvvvvnotavvavcca' returned 4 results
2025-07-30 17:57:09,395 - main - INFO - Search for 'bilalvvvvvnotavvavccal' returned 4 results
2025-07-30 17:57:09,584 - main - INFO - Search for 'bilalvvvvvnotavvavccalo' returned 4 results
2025-07-30 17:57:09,857 - main - INFO - Search for 'bilalvvvvvnotavvavccalog' returned 4 results
2025-07-30 17:57:10,318 - main - INFO - Search for 'bilalvvvvvnotavvavccalo' returned 4 results
2025-07-30 17:57:10,572 - main - INFO - Search for 'bilalvvvvvnotavvavccalon' returned 4 results
2025-07-30 17:57:10,757 - main - INFO - Search for 'bilalvvvvvnotavvavccalong' returned 4 results
2025-07-30 17:57:11,307 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:57:13,298 - main - INFO - Search for 'wi' returned 4 results
2025-07-30 17:57:13,492 - main - INFO - Search for 'wit' returned 4 results
2025-07-30 17:57:13,648 - main - INFO - Search for 'with' returned 4 results
2025-07-30 17:57:13,895 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:57:14,150 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:57:14,318 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:57:14,992 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:57:15,248 - main - INFO - Search for 'te' returned 4 results
2025-07-30 17:57:15,656 - main - INFO - Search for 'tex' returned 4 results
2025-07-30 17:57:15,798 - main - INFO - Search for 'text' returned 4 results
2025-07-30 17:57:17,847 - main - INFO - Search for 'h' returned 4 results
2025-07-30 17:57:18,544 - main - INFO - Search for 'he' returned 4 results
2025-07-30 17:57:18,759 - main - INFO - Search for 'hes' returned 4 results
2025-07-30 17:57:18,927 - main - INFO - Search for 'hese' returned 4 results
2025-07-30 17:57:26,502 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:57:26,600 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:57:26,745 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:57:26,975 - main - INFO - Search for 'thes' returned 4 results
2025-07-30 17:57:27,124 - main - INFO - Search for 'these' returned 4 results
2025-07-30 17:57:27,526 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:57:27,664 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:57:27,730 - main - INFO - Search for 'thi' returned 4 results
2025-07-30 17:57:27,870 - main - INFO - Search for 'thin' returned 4 results
2025-07-30 17:57:27,941 - main - INFO - Search for 'thing' returned 4 results
2025-07-30 17:57:28,807 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:57:29,229 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:57:29,359 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:57:29,468 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:57:29,671 - main - INFO - Search for 'shou' returned 4 results
2025-07-30 17:57:30,014 - main - INFO - Search for 'shoul' returned 4 results
2025-07-30 17:57:30,184 - main - INFO - Search for 'should' returned 4 results
2025-07-30 17:57:30,580 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:57:30,915 - main - INFO - Search for 'as' returned 4 results
2025-07-30 17:57:31,382 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:57:31,712 - main - INFO - Search for 'al' returned 4 results
2025-07-30 17:57:31,828 - main - INFO - Search for 'als' returned 4 results
2025-07-30 17:57:32,412 - main - INFO - Search for 'also' returned 4 results
2025-07-30 17:57:32,799 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:57:32,948 - main - INFO - Search for 'be' returned 4 results
2025-07-30 17:57:33,261 - main - INFO - Search for 'p' returned 4 results
2025-07-30 17:57:33,369 - main - INFO - Search for 'pa' returned 4 results
2025-07-30 17:57:33,568 - main - INFO - Search for 'pas' returned 4 results
2025-07-30 17:57:33,897 - main - INFO - Search for 'past' returned 4 results
2025-07-30 17:57:34,123 - main - INFO - Search for 'paste' returned 4 results
2025-07-30 17:57:34,390 - main - INFO - Search for 'pasted' returned 4 results
2025-07-30 17:57:42,038 - main - INFO - Search for 'F' returned 4 results
2025-07-30 17:57:42,177 - main - INFO - Search for 'Fo' returned 4 results
2025-07-30 17:57:42,302 - main - INFO - Search for 'For' returned 4 results
2025-07-30 17:57:42,832 - main - INFO - Search for 'f' returned 4 results
2025-07-30 17:57:42,978 - main - INFO - Search for 'fr' returned 4 results
2025-07-30 17:57:43,786 - main - INFO - Search for 'f' returned 4 results
2025-07-30 17:57:44,215 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:57:44,404 - main - INFO - Search for 'dr' returned 4 results
2025-07-30 17:57:44,607 - main - INFO - Search for 'dra' returned 4 results
2025-07-30 17:57:44,725 - main - INFO - Search for 'drag' returned 4 results
2025-07-30 17:57:45,010 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:57:45,135 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:57:45,273 - main - INFO - Search for 'and' returned 4 results
2025-07-30 17:57:45,482 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:57:45,665 - main - INFO - Search for 'dr' returned 4 results
2025-07-30 17:57:45,770 - main - INFO - Search for 'dro' returned 4 results
2025-07-30 17:57:45,898 - main - INFO - Search for 'drop' returned 4 results
2025-07-30 17:57:46,316 - main - INFO - Search for 'o' returned 4 results
2025-07-30 17:57:46,511 - main - INFO - Search for 'on' returned 4 results
2025-07-30 17:57:46,707 - main - INFO - Search for 'onl' returned 4 results
2025-07-30 17:57:46,950 - main - INFO - Search for 'only' returned 4 results
2025-07-30 17:57:47,354 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:57:47,483 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:57:47,614 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:57:49,111 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:57:51,098 - main - INFO - Search for 'aa' returned 4 results
2025-07-30 17:57:51,261 - main - INFO - Search for 'aan' returned 4 results
2025-07-30 17:57:51,424 - main - INFO - Search for 'aand' returned 4 results
2025-07-30 17:58:11,439 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:58:11,661 - main - INFO - Search for 'dr' returned 4 results
2025-07-30 17:58:12,428 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:58:13,329 - main - INFO - Search for 'o' returned 4 results
2025-07-30 17:58:13,548 - main - INFO - Search for 'on' returned 4 results
2025-07-30 17:58:13,728 - main - INFO - Search for 'onl' returned 4 results
2025-07-30 17:58:14,039 - main - INFO - Search for 'only' returned 4 results
2025-07-30 17:58:14,532 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:58:14,639 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:58:14,724 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:58:15,018 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:58:15,162 - main - INFO - Search for 'te' returned 4 results
2025-07-30 17:58:15,840 - main - INFO - Search for 'tex' returned 4 results
2025-07-30 17:58:16,395 - main - INFO - Search for 'text' returned 4 results
2025-07-30 17:58:16,824 - main - INFO - Search for 'o' returned 4 results
2025-07-30 17:58:17,001 - main - INFO - Search for 'of' returned 4 results
2025-07-30 17:58:17,300 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:58:17,443 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:58:17,529 - main - INFO - Search for 'tha' returned 4 results
2025-07-30 17:58:17,658 - main - INFO - Search for 'that' returned 4 results
2025-07-30 17:58:17,984 - main - INFO - Search for 'c' returned 4 results
2025-07-30 17:58:18,092 - main - INFO - Search for 'ch' returned 4 results
2025-07-30 17:58:18,262 - main - INFO - Search for 'chu' returned 4 results
2025-07-30 17:58:18,490 - main - INFO - Search for 'chun' returned 4 results
2025-07-30 17:58:18,617 - main - INFO - Search for 'chunk' returned 4 results
2025-07-30 17:58:18,970 - main - INFO - Search for 's' returned 4 results
2025-07-30 17:58:19,122 - main - INFO - Search for 'sh' returned 4 results
2025-07-30 17:58:19,247 - main - INFO - Search for 'sho' returned 4 results
2025-07-30 17:58:19,334 - main - INFO - Search for 'shou' returned 4 results
2025-07-30 17:58:19,533 - main - INFO - Search for 'shoul' returned 4 results
2025-07-30 17:58:19,734 - main - INFO - Search for 'should' returned 4 results
2025-07-30 17:58:20,022 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:58:20,157 - main - INFO - Search for 'be' returned 4 results
2025-07-30 17:58:20,409 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:58:20,611 - main - INFO - Search for 'dr' returned 4 results
2025-07-30 17:58:20,810 - main - INFO - Search for 'dra' returned 4 results
2025-07-30 17:58:20,963 - main - INFO - Search for 'drag' returned 4 results
2025-07-30 17:58:21,383 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:58:21,784 - main - INFO - Search for 'an' returned 4 results
2025-07-30 17:58:21,886 - main - INFO - Search for 'and' returned 4 results
2025-07-30 17:58:22,275 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:58:22,499 - main - INFO - Search for 'dr' returned 4 results
2025-07-30 17:58:22,687 - main - INFO - Search for 'dro' returned 4 results
2025-07-30 17:58:22,876 - main - INFO - Search for 'drop' returned 4 results
2025-07-30 17:58:23,085 - main - INFO - Search for 'dropp' returned 4 results
2025-07-30 17:58:23,289 - main - INFO - Search for 'droppe' returned 4 results
2025-07-30 17:58:23,513 - main - INFO - Search for 'dropped' returned 4 results
2025-07-30 17:58:27,102 - main - INFO - Search for 'S' returned 4 results
2025-07-30 17:58:27,460 - main - INFO - Search for 'So' returned 4 results
2025-07-30 17:58:27,719 - main - INFO - Search for 'Sou' returned 4 results
2025-07-30 17:58:27,892 - main - INFO - Search for 'Sour' returned 4 results
2025-07-30 17:58:28,130 - main - INFO - Search for 'Sourc' returned 4 results
2025-07-30 17:58:28,258 - main - INFO - Search for 'Source' returned 4 results
2025-07-30 17:58:28,791 - main - INFO - Search for 'l' returned 4 results
2025-07-30 17:58:29,031 - main - INFO - Search for 'li' returned 4 results
2025-07-30 17:58:29,191 - main - INFO - Search for 'lin' returned 4 results
2025-07-30 17:58:29,446 - main - INFO - Search for 'link' returned 4 results
2025-07-30 17:58:29,796 - main - INFO - Search for 'p' returned 4 results
2025-07-30 17:58:29,951 - main - INFO - Search for 'pa' returned 4 results
2025-07-30 17:58:30,230 - main - INFO - Search for 'pag' returned 4 results
2025-07-30 17:58:30,383 - main - INFO - Search for 'page' returned 4 results
2025-07-30 17:58:30,935 - main - INFO - Search for 'n' returned 4 results
2025-07-30 17:58:31,060 - main - INFO - Search for 'no' returned 4 results
2025-07-30 17:58:31,572 - main - INFO - Search for 'a' returned 4 results
2025-07-30 17:58:31,843 - main - INFO - Search for 'ad' returned 4 results
2025-07-30 17:58:33,394 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:58:33,514 - main - INFO - Search for 'do' returned 4 results
2025-07-30 17:58:33,655 - main - INFO - Search for 'doc' returned 4 results
2025-07-30 17:58:33,823 - main - INFO - Search for 'docu' returned 4 results
2025-07-30 17:58:34,056 - main - INFO - Search for 'docum' returned 4 results
2025-07-30 17:58:34,179 - main - INFO - Search for 'docume' returned 4 results
2025-07-30 17:58:34,350 - main - INFO - Search for 'documen' returned 4 results
2025-07-30 17:58:34,444 - main - INFO - Search for 'document' returned 4 results
2025-07-30 17:58:34,795 - main - INFO - Search for 'n' returned 4 results
2025-07-30 17:58:34,863 - main - INFO - Search for 'na' returned 4 results
2025-07-30 17:58:34,981 - main - INFO - Search for 'nam' returned 4 results
2025-07-30 17:58:35,134 - main - INFO - Search for 'name' returned 4 results
2025-07-30 17:58:36,095 - main - INFO - Search for 'w' returned 4 results
2025-07-30 17:58:36,240 - main - INFO - Search for 'wi' returned 4 results
2025-07-30 17:58:36,465 - main - INFO - Search for 'wil' returned 4 results
2025-07-30 17:58:36,634 - main - INFO - Search for 'will' returned 4 results
2025-07-30 17:58:36,951 - main - INFO - Search for 'b' returned 4 results
2025-07-30 17:58:37,093 - main - INFO - Search for 'be' returned 4 results
2025-07-30 17:58:37,365 - main - INFO - Search for 'd' returned 4 results
2025-07-30 17:58:37,535 - main - INFO - Search for 'di' returned 4 results
2025-07-30 17:58:37,838 - main - INFO - Search for 'dip' returned 4 results
2025-07-30 17:58:37,862 - main - INFO - Search for 'dips' returned 4 results
2025-07-30 17:58:38,107 - main - INFO - Search for 'dipsl' returned 4 results
2025-07-30 17:58:38,384 - main - INFO - Search for 'dipsla' returned 4 results
2025-07-30 17:58:38,478 - main - INFO - Search for 'dipslay' returned 4 results
2025-07-30 17:58:38,609 - main - INFO - Search for 'dipslaye' returned 4 results
2025-07-30 17:58:38,777 - main - INFO - Search for 'dipslayed' returned 4 results
2025-07-30 17:58:39,144 - main - INFO - Search for 'i' returned 4 results
2025-07-30 17:58:39,181 - main - INFO - Search for 'in' returned 4 results
2025-07-30 17:58:39,522 - main - INFO - Search for 't' returned 4 results
2025-07-30 17:58:39,672 - main - INFO - Search for 'th' returned 4 results
2025-07-30 17:58:39,803 - main - INFO - Search for 'the' returned 4 results
2025-07-30 17:58:41,795 - main - INFO - Search for 'r' returned 4 results
2025-07-30 17:58:41,831 - main - INFO - Search for 're' returned 4 results
2025-07-30 17:58:42,055 - main - INFO - Search for 'res' returned 4 results
2025-07-30 17:58:42,234 - main - INFO - Search for 'resu' returned 4 results
2025-07-30 17:58:42,339 - main - INFO - Search for 'resul' returned 4 results
2025-07-30 17:58:42,461 - main - INFO - Search for 'result' returned 4 results
2025-07-30 17:58:42,794 - main - INFO - Search for 'l' returned 4 results
2025-07-30 17:58:42,954 - main - INFO - Search for 'li' returned 4 results
2025-07-30 17:58:43,062 - main - INFO - Search for 'lis' returned 4 results
2025-07-30 17:58:43,253 - main - INFO - Search for 'list' returned 4 results
2025-07-30 17:58:45,045 - main - INFO - Search for 'listr' returned 4 results
2025-07-30 17:58:45,171 - main - INFO - Search for 'listri' returned 4 results
2025-07-30 17:58:45,356 - main - INFO - Search for 'listrig' returned 4 results
2025-07-30 17:58:45,485 - main - INFO - Search for 'listrigh' returned 4 results
2025-07-30 17:58:45,573 - main - INFO - Search for 'listright' returned 4 results
2025-07-30 17:58:46,807 - main - INFO - Search for 'listrighta' returned 4 results
2025-07-30 17:58:47,080 - main - INFO - Search for 'listrightac' returned 4 results
2025-07-30 17:58:49,799 - main - INFO - Search for 'listrightacv' returned 4 results
