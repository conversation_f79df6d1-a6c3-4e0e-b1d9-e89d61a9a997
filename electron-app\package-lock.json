{"name": "semantic-search-assistant", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "semantic-search-assistant", "version": "1.0.0", "license": "MIT", "dependencies": {"axios": "^1.6.0", "electron-is-dev": "^2.0.0", "electron-store": "^8.1.0"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}}, "node_modules/@develar/schema-utils": {"version": "2.6.5", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/@electron/asar": {"version": "3.4.1", "dev": true, "license": "MIT", "dependencies": {"commander": "^5.0.0", "glob": "^7.1.6", "minimatch": "^3.0.4"}, "bin": {"asar": "bin/asar.js"}, "engines": {"node": ">=10.12.0"}}, "node_modules/@electron/asar/node_modules/brace-expansion": {"version": "1.1.12", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@electron/asar/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@electron/get": {"version": "2.0.3", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "env-paths": "^2.2.0", "fs-extra": "^8.1.0", "got": "^11.8.5", "progress": "^2.0.3", "semver": "^6.2.0", "sumchecker": "^3.0.1"}, "engines": {"node": ">=12"}, "optionalDependencies": {"global-agent": "^3.0.0"}}, "node_modules/@electron/notarize": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "fs-extra": "^9.0.1", "promise-retry": "^2.0.1"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@electron/notarize/node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@electron/notarize/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@electron/notarize/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@electron/osx-sign": {"version": "1.0.5", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"compare-version": "^0.1.2", "debug": "^4.3.4", "fs-extra": "^10.0.0", "isbinaryfile": "^4.0.8", "minimist": "^1.2.6", "plist": "^3.0.5"}, "bin": {"electron-osx-flat": "bin/electron-osx-flat.js", "electron-osx-sign": "bin/electron-osx-sign.js"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@electron/osx-sign/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@electron/osx-sign/node_modules/isbinaryfile": {"version": "4.0.10", "dev": true, "license": "MIT", "engines": {"node": ">= 8.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}}, "node_modules/@electron/osx-sign/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@electron/osx-sign/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@electron/universal": {"version": "1.5.1", "dev": true, "license": "MIT", "dependencies": {"@electron/asar": "^3.2.1", "@malept/cross-spawn-promise": "^1.1.0", "debug": "^4.3.1", "dir-compare": "^3.0.0", "fs-extra": "^9.0.1", "minimatch": "^3.0.4", "plist": "^3.0.4"}, "engines": {"node": ">=8.6"}}, "node_modules/@electron/universal/node_modules/brace-expansion": {"version": "1.1.12", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@electron/universal/node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@electron/universal/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@electron/universal/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@electron/universal/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "dev": true, "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "dev": true, "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@malept/cross-spawn-promise": {"version": "1.1.1", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/malept"}, {"type": "tidelift", "url": "https://tidelift.com/subscription/pkg/npm-.malept-cross-spawn-promise?utm_medium=referral&utm_source=npm_fund"}], "license": "Apache-2.0", "dependencies": {"cross-spawn": "^7.0.1"}, "engines": {"node": ">= 10"}}, "node_modules/@malept/flatpak-bundler": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "fs-extra": "^9.0.0", "lodash": "^4.17.15", "tmp-promise": "^3.0.2"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@malept/flatpak-bundler/node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@malept/flatpak-bundler/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@malept/flatpak-bundler/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@sindresorhus/is": {"version": "4.6.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/is?sponsor=1"}}, "node_modules/@szmarczak/http-timer": {"version": "4.0.6", "dev": true, "license": "MIT", "dependencies": {"defer-to-connect": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@tootallnate/once": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/@types/cacheable-request": {"version": "6.0.3", "dev": true, "license": "MIT", "dependencies": {"@types/http-cache-semantics": "*", "@types/keyv": "^3.1.4", "@types/node": "*", "@types/responselike": "^1.0.0"}}, "node_modules/@types/debug": {"version": "4.1.12", "dev": true, "license": "MIT", "dependencies": {"@types/ms": "*"}}, "node_modules/@types/fs-extra": {"version": "9.0.13", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/http-cache-semantics": {"version": "4.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/keyv": {"version": "3.1.4", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/ms": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "18.19.115", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@types/plist": {"version": "3.0.5", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*", "xmlbuilder": ">=11.0.1"}}, "node_modules/@types/responselike": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/verror": {"version": "1.10.11", "dev": true, "license": "MIT", "optional": true}, "node_modules/@types/yauzl": {"version": "2.10.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*"}}, "node_modules/@xmldom/xmldom": {"version": "0.8.10", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/7zip-bin": {"version": "5.2.0", "dev": true, "license": "MIT"}, "node_modules/agent-base": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats": {"version": "2.1.1", "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/ajv-formats/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/ajv-keywords": {"version": "3.5.2", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/app-builder-lib": {"version": "24.13.3", "dev": true, "license": "MIT", "dependencies": {"@develar/schema-utils": "~2.6.5", "@electron/notarize": "2.2.1", "@electron/osx-sign": "1.0.5", "@electron/universal": "1.5.1", "@malept/flatpak-bundler": "^0.4.0", "@types/fs-extra": "9.0.13", "async-exit-hook": "^2.0.1", "bluebird-lst": "^1.0.9", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "chromium-pickle-js": "^0.2.0", "debug": "^4.3.4", "ejs": "^3.1.8", "electron-publish": "24.13.1", "form-data": "^4.0.0", "fs-extra": "^10.1.0", "hosted-git-info": "^4.1.0", "is-ci": "^3.0.0", "isbinaryfile": "^5.0.0", "js-yaml": "^4.1.0", "lazy-val": "^1.0.5", "minimatch": "^5.1.1", "read-config-file": "6.3.2", "sanitize-filename": "^1.6.3", "semver": "^7.3.8", "tar": "^6.1.12", "temp-file": "^3.4.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"dmg-builder": "24.13.3", "electron-builder-squirrel-windows": "24.13.3"}}, "node_modules/app-builder-lib/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/app-builder-lib/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/app-builder-lib/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/app-builder-lib/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/archiver": {"version": "5.3.2", "dev": true, "license": "MIT", "peer": true, "dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "engines": {"node": ">= 10"}}, "node_modules/archiver-utils": {"version": "2.1.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "engines": {"node": ">= 6"}}, "node_modules/archiver-utils/node_modules/readable-stream": {"version": "2.3.8", "dev": true, "license": "MIT", "peer": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/archiver-utils/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT", "peer": true}, "node_modules/archiver-utils/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/assert-plus": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/astral-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/async": {"version": "3.2.6", "dev": true, "license": "MIT"}, "node_modules/async-exit-hook": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "dev": true, "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/atomically": {"version": "1.7.0", "license": "MIT", "engines": {"node": ">=10.12.0"}}, "node_modules/axios": {"version": "1.10.0", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bl": {"version": "4.1.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/bluebird": {"version": "3.7.2", "dev": true, "license": "MIT"}, "node_modules/bluebird-lst": {"version": "1.0.9", "dev": true, "license": "MIT", "dependencies": {"bluebird": "^3.5.5"}}, "node_modules/boolean": {"version": "3.2.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/brace-expansion": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/buffer": {"version": "5.7.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/buffer-equal": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/buffer-from": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/builder-util": {"version": "24.13.1", "dev": true, "license": "MIT", "dependencies": {"@types/debug": "^4.1.6", "7zip-bin": "~5.2.0", "app-builder-bin": "4.0.0", "bluebird-lst": "^1.0.9", "builder-util-runtime": "9.2.4", "chalk": "^4.1.2", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "fs-extra": "^10.1.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "is-ci": "^3.0.0", "js-yaml": "^4.1.0", "source-map-support": "^0.5.19", "stat-mode": "^1.0.0", "temp-file": "^3.4.0"}}, "node_modules/builder-util-runtime": {"version": "9.2.4", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4", "sax": "^1.2.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/builder-util/node_modules/app-builder-bin": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/app-builder-bin/-/app-builder-bin-4.0.0.tgz", "integrity": "sha512-xwdG0FJPQMe0M0UA4Tz0zEB8rBJTRA5a476ZawAqiBkMv16GRK5xpXThOjMaEOFnZ6zabejjG4J3da0SXG63KA==", "dev": true, "license": "MIT"}, "node_modules/builder-util/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/builder-util/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/builder-util/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/cacheable-lookup": {"version": "5.0.4", "dev": true, "license": "MIT", "engines": {"node": ">=10.6.0"}}, "node_modules/cacheable-request": {"version": "7.0.4", "dev": true, "license": "MIT", "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^4.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^6.0.1", "responselike": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/chownr": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/chromium-pickle-js": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/ci-info": {"version": "3.9.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cli-truncate": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"slice-ansi": "^3.0.0", "string-width": "^4.2.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cliui": {"version": "8.0.1", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/clone-response": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "5.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/compare-version": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/compress-commons": {"version": "4.1.2", "dev": true, "license": "MIT", "peer": true, "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.2", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 10"}}, "node_modules/concat-map": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/conf": {"version": "10.2.0", "license": "MIT", "dependencies": {"ajv": "^8.6.3", "ajv-formats": "^2.1.1", "atomically": "^1.7.0", "debounce-fn": "^4.0.0", "dot-prop": "^6.0.1", "env-paths": "^2.2.1", "json-schema-typed": "^7.0.3", "onetime": "^5.1.2", "pkg-up": "^3.1.0", "semver": "^7.3.5"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/conf/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/conf/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/conf/node_modules/semver": {"version": "7.7.2", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/config-file-ts": {"version": "0.2.6", "dev": true, "license": "MIT", "dependencies": {"glob": "^10.3.10", "typescript": "^5.3.3"}}, "node_modules/config-file-ts/node_modules/glob": {"version": "10.4.5", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/config-file-ts/node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/config-file-ts/node_modules/minipass": {"version": "7.1.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/core-util-is": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/crc": {"version": "3.8.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"buffer": "^5.1.0"}}, "node_modules/crc-32": {"version": "1.2.2", "dev": true, "license": "Apache-2.0", "peer": true, "bin": {"crc32": "bin/crc32.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/crc32-stream": {"version": "4.0.3", "dev": true, "license": "MIT", "peer": true, "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "engines": {"node": ">= 10"}}, "node_modules/cross-spawn": {"version": "7.0.6", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/debounce-fn": {"version": "4.0.0", "license": "MIT", "dependencies": {"mimic-fn": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/debug": {"version": "4.4.1", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decompress-response": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"mimic-response": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/decompress-response/node_modules/mimic-response": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/defer-to-connect": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/define-data-property": {"version": "1.1.4", "dev": true, "license": "MIT", "optional": true, "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/detect-node": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/dir-compare": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"buffer-equal": "^1.0.0", "minimatch": "^3.0.4"}}, "node_modules/dir-compare/node_modules/brace-expansion": {"version": "1.1.12", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/dir-compare/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/dmg-builder": {"version": "24.13.3", "dev": true, "license": "MIT", "dependencies": {"app-builder-lib": "24.13.3", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "fs-extra": "^10.1.0", "iconv-lite": "^0.6.2", "js-yaml": "^4.1.0"}, "optionalDependencies": {"dmg-license": "^1.0.11"}}, "node_modules/dmg-builder/node_modules/dmg-license": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/dmg-license/-/dmg-license-1.0.11.tgz", "integrity": "sha512-ZdzmqwKmECOWJpqefloC5OJy1+WZBBse5+MR88z9g9Zn4VY+WYUkAyojmhzJckH5YbbZGcYIuGAkY5/Ys5OM2Q==", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "dependencies": {"@types/plist": "^3.0.1", "@types/verror": "^1.10.3", "ajv": "^6.10.0", "crc": "^3.8.0", "iconv-corefoundation": "^1.1.7", "plist": "^3.0.4", "smart-buffer": "^4.0.2", "verror": "^1.10.0"}, "bin": {"dmg-license": "bin/dmg-license.js"}, "engines": {"node": ">=8"}}, "node_modules/dmg-builder/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/dmg-builder/node_modules/iconv-corefoundation": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/iconv-corefoundation/-/iconv-corefoundation-1.1.7.tgz", "integrity": "sha512-T10qvkw0zz4wnm560lOEg0PovVqUXuOFhhHAkixw8/sycy7TJt7v/RrkEKEQnAw2viPSJu6iAkErxnzR0g8PpQ==", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "dependencies": {"cli-truncate": "^2.1.0", "node-addon-api": "^1.6.3"}, "engines": {"node": "^8.11.2 || >=10"}}, "node_modules/dmg-builder/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/dmg-builder/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/dot-prop": {"version": "6.0.1", "license": "MIT", "dependencies": {"is-obj": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/dotenv": {"version": "9.0.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/dotenv-expand": {"version": "5.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/ejs": {"version": "3.1.10", "dev": true, "license": "Apache-2.0", "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/electron": {"version": "27.3.11", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@electron/get": "^2.0.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1"}, "bin": {"electron": "cli.js"}, "engines": {"node": ">= 12.20.55"}}, "node_modules/electron-builder": {"version": "24.13.3", "dev": true, "license": "MIT", "dependencies": {"app-builder-lib": "24.13.3", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "chalk": "^4.1.2", "dmg-builder": "24.13.3", "fs-extra": "^10.1.0", "is-ci": "^3.0.0", "lazy-val": "^1.0.5", "read-config-file": "6.3.2", "simple-update-notifier": "2.0.0", "yargs": "^17.6.2"}, "bin": {"electron-builder": "cli.js", "install-app-deps": "install-app-deps.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/electron-builder-squirrel-windows": {"version": "24.13.3", "dev": true, "license": "MIT", "peer": true, "dependencies": {"app-builder-lib": "24.13.3", "archiver": "^5.3.1", "builder-util": "24.13.1", "fs-extra": "^10.1.0"}}, "node_modules/electron-builder-squirrel-windows/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/electron-builder-squirrel-windows/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/electron-builder-squirrel-windows/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">= 10.0.0"}}, "node_modules/electron-builder/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/electron-builder/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/electron-builder/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/electron-is-dev": {"version": "2.0.0", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/electron-publish": {"version": "24.13.1", "dev": true, "license": "MIT", "dependencies": {"@types/fs-extra": "^9.0.11", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "chalk": "^4.1.2", "fs-extra": "^10.1.0", "lazy-val": "^1.0.5", "mime": "^2.5.2"}}, "node_modules/electron-publish/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/electron-publish/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/electron-publish/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/electron-store": {"version": "8.2.0", "license": "MIT", "dependencies": {"conf": "^10.2.0", "type-fest": "^2.17.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/end-of-stream": {"version": "1.4.5", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/env-paths": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/err-code": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es6-error": {"version": "4.1.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/escalade": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/extract-zip": {"version": "2.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}, "bin": {"extract-zip": "cli.js"}, "engines": {"node": ">= 10.17.0"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}}, "node_modules/extsprintf": {"version": "1.4.1", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT", "optional": true}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-uri": {"version": "3.0.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/fd-slicer": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"pend": "~1.2.0"}}, "node_modules/filelist": {"version": "1.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/find-up": {"version": "3.0.0", "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/foreground-child": {"version": "3.3.1", "dev": true, "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/form-data": {"version": "4.0.3", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/fs-constants": {"version": "1.0.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/fs-extra": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-minipass": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-caller-file": {"version": "2.0.5", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob": {"version": "7.2.3", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob/node_modules/brace-expansion": {"version": "1.1.12", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/glob/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/global-agent": {"version": "3.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"boolean": "^3.0.1", "es6-error": "^4.1.1", "matcher": "^3.0.0", "roarr": "^2.15.3", "semver": "^7.3.2", "serialize-error": "^7.0.1"}, "engines": {"node": ">=10.0"}}, "node_modules/global-agent/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/globalthis": {"version": "1.0.4", "dev": true, "license": "MIT", "optional": true, "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/got": {"version": "11.8.6", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/is": "^4.0.0", "@szmarczak/http-timer": "^4.0.5", "@types/cacheable-request": "^6.0.1", "@types/responselike": "^1.0.0", "cacheable-lookup": "^5.0.3", "cacheable-request": "^7.0.2", "decompress-response": "^6.0.0", "http2-wrapper": "^1.0.0-beta.5.2", "lowercase-keys": "^2.0.0", "p-cancelable": "^2.0.0", "responselike": "^2.0.0"}, "engines": {"node": ">=10.19.0"}, "funding": {"url": "https://github.com/sindresorhus/got?sponsor=1"}}, "node_modules/graceful-fs": {"version": "4.2.11", "dev": true, "license": "ISC"}, "node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "dev": true, "license": "MIT", "optional": true, "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hosted-git-info": {"version": "4.1.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/http-cache-semantics": {"version": "4.2.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-proxy-agent": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/http2-wrapper": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "engines": {"node": ">=10.19.0"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/inflight": {"version": "1.0.6", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/is-ci": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"ci-info": "^3.2.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-obj": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/isbinaryfile": {"version": "5.0.4", "dev": true, "license": "MIT", "engines": {"node": ">= 18.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}}, "node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/jackspeak": {"version": "3.4.3", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jake": {"version": "10.9.2", "dev": true, "license": "Apache-2.0", "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/jake/node_modules/brace-expansion": {"version": "1.1.12", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/jake/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-typed": {"version": "7.0.3", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "dev": true, "license": "ISC", "optional": true}, "node_modules/json5": {"version": "2.2.3", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "4.0.0", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/lazy-val": {"version": "1.0.5", "dev": true, "license": "MIT"}, "node_modules/lazystream": {"version": "1.0.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"readable-stream": "^2.0.5"}, "engines": {"node": ">= 0.6.3"}}, "node_modules/lazystream/node_modules/readable-stream": {"version": "2.3.8", "dev": true, "license": "MIT", "peer": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/lazystream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT", "peer": true}, "node_modules/lazystream/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/locate-path": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/lodash": {"version": "4.17.21", "dev": true, "license": "MIT"}, "node_modules/lodash.defaults": {"version": "4.2.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/lodash.difference": {"version": "4.5.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/lodash.flatten": {"version": "4.4.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "dev": true, "license": "MIT", "peer": true}, "node_modules/lodash.union": {"version": "4.6.0", "dev": true, "license": "MIT", "peer": true}, "node_modules/lowercase-keys": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/matcher": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"escape-string-regexp": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/mime": {"version": "2.6.0", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/mimic-response": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "5.1.6", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/minimist": {"version": "1.2.8", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "5.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/mkdirp": {"version": "1.0.4", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.3", "dev": true, "license": "MIT"}, "node_modules/node-addon-api": {"version": "1.7.2", "dev": true, "license": "MIT", "optional": true}, "node_modules/normalize-path": {"version": "3.0.0", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/object-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/onetime/node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/p-cancelable": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "dev": true, "license": "BlueOak-1.0.0"}, "node_modules/path-exists": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-scurry": {"version": "1.11.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "node_modules/pend": {"version": "1.2.0", "dev": true, "license": "MIT"}, "node_modules/pkg-up": {"version": "3.1.0", "license": "MIT", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/plist": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}, "engines": {"node": ">=10.4.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "dev": true, "license": "MIT", "peer": true}, "node_modules/progress": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/promise-retry": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/pump": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/quick-lru": {"version": "5.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/read-config-file": {"version": "6.3.2", "dev": true, "license": "MIT", "dependencies": {"config-file-ts": "^0.2.4", "dotenv": "^9.0.2", "dotenv-expand": "^5.1.0", "js-yaml": "^4.1.0", "json5": "^2.2.0", "lazy-val": "^1.0.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/readable-stream": {"version": "3.6.2", "dev": true, "license": "MIT", "peer": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdir-glob": {"version": "1.1.3", "dev": true, "license": "Apache-2.0", "peer": true, "dependencies": {"minimatch": "^5.1.0"}}, "node_modules/require-directory": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/resolve-alpn": {"version": "1.2.1", "dev": true, "license": "MIT"}, "node_modules/responselike": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"lowercase-keys": "^2.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/retry": {"version": "0.12.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/roarr": {"version": "2.15.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"boolean": "^3.0.1", "detect-node": "^2.0.4", "globalthis": "^1.0.1", "json-stringify-safe": "^5.0.1", "semver-compare": "^1.0.0", "sprintf-js": "^1.1.2"}, "engines": {"node": ">=8.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "peer": true}, "node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/sanitize-filename": {"version": "1.6.3", "dev": true, "license": "WTFPL OR ISC", "dependencies": {"truncate-utf8-bytes": "^1.0.0"}}, "node_modules/sax": {"version": "1.4.1", "dev": true, "license": "ISC"}, "node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/semver-compare": {"version": "1.0.0", "dev": true, "license": "MIT", "optional": true}, "node_modules/serialize-error": {"version": "7.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"type-fest": "^0.13.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/serialize-error/node_modules/type-fest": {"version": "0.13.1", "dev": true, "license": "(MIT OR CC0-1.0)", "optional": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/simple-update-notifier": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}}, "node_modules/simple-update-notifier/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/slice-ansi": {"version": "3.0.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/smart-buffer": {"version": "4.2.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/sprintf-js": {"version": "1.1.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true}, "node_modules/stat-mode": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/string_decoder": {"version": "1.3.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-width": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/sumchecker": {"version": "3.0.1", "dev": true, "license": "Apache-2.0", "dependencies": {"debug": "^4.1.0"}, "engines": {"node": ">= 8.0"}}, "node_modules/tar": {"version": "6.2.1", "dev": true, "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar-stream": {"version": "2.2.0", "dev": true, "license": "MIT", "peer": true, "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/temp-file": {"version": "3.4.0", "dev": true, "license": "MIT", "dependencies": {"async-exit-hook": "^2.0.1", "fs-extra": "^10.0.0"}}, "node_modules/temp-file/node_modules/fs-extra": {"version": "10.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/temp-file/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/temp-file/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/tmp": {"version": "0.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/tmp-promise": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"tmp": "^0.2.0"}}, "node_modules/truncate-utf8-bytes": {"version": "1.0.2", "dev": true, "license": "WTFPL", "dependencies": {"utf8-byte-length": "^1.0.1"}}, "node_modules/type-fest": {"version": "2.19.0", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typescript": {"version": "5.8.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "5.26.5", "dev": true, "license": "MIT"}, "node_modules/universalify": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/utf8-byte-length": {"version": "1.0.5", "dev": true, "license": "(WTFPL OR MIT)"}, "node_modules/util-deprecate": {"version": "1.0.2", "dev": true, "license": "MIT", "peer": true}, "node_modules/verror": {"version": "1.10.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/xmlbuilder": {"version": "15.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8.0"}}, "node_modules/y18n": {"version": "5.0.8", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/yargs": {"version": "17.7.2", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yauzl": {"version": "2.10.0", "dev": true, "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "node_modules/zip-stream": {"version": "4.1.1", "dev": true, "license": "MIT", "peer": true, "dependencies": {"archiver-utils": "^3.0.4", "compress-commons": "^4.1.2", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 10"}}, "node_modules/zip-stream/node_modules/archiver-utils": {"version": "3.0.4", "dev": true, "license": "MIT", "peer": true, "dependencies": {"glob": "^7.2.3", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 10"}}}}