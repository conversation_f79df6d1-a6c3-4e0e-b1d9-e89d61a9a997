@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Custom button styles */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
  
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-sm {
    @apply h-9 px-3;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-11 px-8;
  }
  
  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Card styles */
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  /* Search result styles */
  .search-result {
    @apply p-4 border-b border-border hover:bg-muted/50 transition-colors cursor-pointer;
  }
  
  .search-result:last-child {
    @apply border-b-0;
  }
  
  .search-result-title {
    @apply font-medium text-foreground mb-1;
  }
  
  .search-result-snippet {
    @apply text-sm text-muted-foreground mb-2;
  }
  
  .search-result-metadata {
    @apply text-xs text-muted-foreground flex items-center gap-2;
  }
  
  /* Highlight styles */
  .highlight {
    @apply bg-yellow-200 dark:bg-yellow-800 px-1 rounded;
  }
  
  .readwise-highlight {
    @apply border-l-4 border-blue-500 pl-3;
  }
  
  /* Floating window specific styles */
  .floating-window {
    @apply h-full flex flex-col bg-background/95 backdrop-blur-md;
  }
  
  .floating-header {
    @apply flex items-center justify-between p-3 border-b border-border/50;
  }
  
  .floating-content {
    @apply flex-1 overflow-hidden;
  }
  
  /* Drag handle */
  .drag-handle {
    @apply cursor-move select-none;
  }

  /* Electron drag regions */
  .drag-region {
    -webkit-app-region: drag;
    cursor: move;
  }

  .no-drag {
    -webkit-app-region: no-drag;
    cursor: default;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-muted border-t-primary;
  }
  
  /* Scrollbar styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted));
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }
}

/* Animation utilities */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
}

/* Drag and drop styles */
.drag-preview {
  @apply bg-primary text-primary-foreground px-3 py-2 rounded-md shadow-lg;
}

.drop-zone {
  @apply border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center text-muted-foreground;
}

.drop-zone.active {
  @apply border-primary bg-primary/5 text-primary;
}
